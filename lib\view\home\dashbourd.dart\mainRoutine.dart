import 'dart:convert';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/alarm.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/view/home/<USER>/routine.dart';
import 'package:zaen/view/home/<USER>/settingFolder/routineWord.dart';
import 'package:zaen/view/home/<USER>/settingFolder/tasks.dart';
import 'package:zaen/view/room/double_tap/edit_names.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/view/home/<USER>/routineComponents/routineDatabaseOperations.dart';
import 'package:zaen/view/home/<USER>/routineComponents/routineIconSelector.dart';
import 'package:zaen/view/home/<USER>/routineComponents/routineDetailsDisplay.dart';
import 'package:zaen/view/home/<USER>/routineComponents/routineFormLogic.dart';

Widget mainRoutine({context, setState, edit}) {
  // String routineIcon = '1';
  GlobalKey<FormState> kname1 = new GlobalKey<FormState>();
  GlobalKey<FormState> kname2 = new GlobalKey<FormState>();
  return GestureDetector(
    onTap: () {
      FocusManager.instance.primaryFocus?.unfocus();
      if (kname1.currentState != null) {
        var formdata = kname1.currentState;
        formdata!.validate();
      }
      if (kname2.currentState != null) {
        var formdata = kname2.currentState;
        formdata!.validate();
      }
    },
    child: SingleChildScrollView(
      child: Column(
        children: [
          Row(children: [
            Padding(
              padding: EdgeInsets.only(
                  right: controller.sizedWidth * 0.03,
                  top: controller.sizedHight * 0.03,
                  bottom: controller.sizedHight * 0.01),
              child: txtStyle(
                  txt: 'المهمه',
                  align: TextAlign.start,
                  color: AppColors.textColor2.withOpacity(0.5),
                  size: controller.sized * 0.012),
            ),
          ]),
          containerPageOption(
            content: ExpansionTileCard(
                duration: Duration(milliseconds: 400),
                baseColor: Colors.transparent,
                expandedColor: Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(17)),
                shadowColor: Colors.transparent,
                contentPadding: EdgeInsets.zero,
                trailing: Icon(
                  Icons.check_circle_rounded,
                  color: AppColors.primaryColor,
                  size: controller.sized * 0.027,
                ),
                title: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    iconStyle(
                        icon: Icons.task_alt, color: AppColors.warningColor),
                    SizedBox(
                      width: controller.sizedWidth * 0.02,
                    ),
                    Expanded(
                      child: txtStyle(
                        align: TextAlign.start,
                        txt: 'المهمه المختارة',
                      ),
                    ),
                  ],
                ),
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.02),
                    child: Divider(
                      color: AppColors.textColor2.withOpacity(0.3),
                      endIndent: 2,
                      indent: 2,
                    ),
                  ),
                  controller.addRoutine.containsKey('home')
                      ? Text(
                          controller.addRoutine['home'] == true
                              ? 'تشغيل ${controller.home}'
                              : 'اغلاق ${controller.home}',
                          style: TextStyle(
                            fontSize: controller.sized * 0.013,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textColor2.withOpacity(0.75),
                          ))
                      : Container(
                          height: controller.sizedHight * 0.35,
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                for (var r
                                    in controller.addRoutine.keys.toList())
                                  if (controller.addRoutine[r] == true ||
                                      controller.addRoutine[r] == false)
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            controller.addRoutine[r] == true
                                                ? 'تشغيل ${controller.rooms[r]['privName']}'
                                                : 'اغلاق ${controller.rooms[r]['privName']}',
                                            style: TextStyle(
                                              fontSize:
                                                  controller.sized * 0.013,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor2
                                                  .withOpacity(0.75),
                                            )),
                                        Divider(
                                          color: AppColors.textColor2
                                              .withOpacity(0.3),
                                          endIndent: 2,
                                          indent: 2,
                                        )
                                      ],
                                    )
                                  else
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            '${controller.rooms[r]['privName']}',
                                            style: TextStyle(
                                              fontSize:
                                                  controller.sized * 0.013,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor2
                                                  .withOpacity(0.75),
                                            )),
                                        for (var d in controller
                                            .addRoutine[r].keys
                                            .toList())
                                          controller.rooms[r]['devices'][d]
                                                      ['device'] ==
                                                  'AC'
                                              ? Padding(
                                                  padding: EdgeInsets.only(
                                                      right: controller
                                                              .sizedWidth *
                                                          0.06),
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                          '- مكيف : ' +
                                                              (controller.rooms[
                                                                              r]
                                                                              [
                                                                              'devices']
                                                                              [
                                                                              d]
                                                                              [
                                                                              'priv']
                                                                          .toString() ==
                                                                      'x'
                                                                  ? 'لا يوجد اسم'
                                                                  : controller
                                                                      .rooms[r][
                                                                          'devices']
                                                                          [d][
                                                                          'priv']
                                                                      .toString()),
                                                          style: TextStyle(
                                                            fontSize: controller
                                                                    .sized *
                                                                0.013,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: AppColors
                                                                .textColor2
                                                                .withOpacity(
                                                                    0.75),
                                                          )),
                                                      Padding(
                                                        padding: EdgeInsets.only(
                                                            right: controller
                                                                    .sizedWidth *
                                                                0.06),
                                                        child: Text(
                                                            controller.addRoutine[
                                                                            r][d]
                                                                        [
                                                                        'state'] ==
                                                                    true
                                                                ? '*  تشغيل المكيف'
                                                                : '*  اغلاق المكيف',
                                                            style: TextStyle(
                                                              fontSize: controller
                                                                      .sized *
                                                                  0.013,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color: AppColors
                                                                  .textColor2
                                                                  .withOpacity(
                                                                      0.75),
                                                            )),
                                                      ),
                                                      controller.addRoutine[r]
                                                                          [d][
                                                                      'state'] ==
                                                                  true &&
                                                              controller.addRoutine[
                                                                          r][d][
                                                                      'degree'] !=
                                                                  null
                                                          ? Padding(
                                                              padding: EdgeInsets.only(
                                                                  right: controller
                                                                          .sizedWidth *
                                                                      0.06),
                                                              child: Text(
                                                                  '*  درجة التكييف : ${controller.addRoutine[r][d]['degree']}°',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        controller.sized *
                                                                            0.013,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color: AppColors
                                                                        .textColor2
                                                                        .withOpacity(
                                                                            0.75),
                                                                  )),
                                                            )
                                                          : Container(),
                                                      controller.addRoutine[r]
                                                                          [d][
                                                                      'state'] ==
                                                                  true &&
                                                              controller.addRoutine[
                                                                          r][d][
                                                                      'type'] !=
                                                                  null
                                                          ? Padding(
                                                              padding: EdgeInsets.only(
                                                                  right: controller
                                                                          .sizedWidth *
                                                                      0.06),
                                                              child: Text(
                                                                  '*  نوع التكييف :  ${controller.addRoutine[r][d]['type']}',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        controller.sized *
                                                                            0.013,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color: AppColors
                                                                        .textColor2
                                                                        .withOpacity(
                                                                            0.75),
                                                                  )),
                                                            )
                                                          : Container(),
                                                      controller.addRoutine[r]
                                                                          [d][
                                                                      'state'] ==
                                                                  true &&
                                                              controller.addRoutine[
                                                                          r][d][
                                                                      'speed'] !=
                                                                  null
                                                          ? Padding(
                                                              padding: EdgeInsets.only(
                                                                  right: controller
                                                                          .sizedWidth *
                                                                      0.06),
                                                              child: Text(
                                                                  '*  سرعة المروحة : ${controller.addRoutine[r][d]['speed'] == '4' ? 'متغيرة' : controller.addRoutine[r][d]['speed']}',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        controller.sized *
                                                                            0.013,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color: AppColors
                                                                        .textColor2
                                                                        .withOpacity(
                                                                            0.75),
                                                                  )),
                                                            )
                                                          : Container(),
                                                      controller.addRoutine[r]
                                                                          [d][
                                                                      'state'] ==
                                                                  true &&
                                                              controller.addRoutine[
                                                                          r][d][
                                                                      'swing'] !=
                                                                  null
                                                          ? Padding(
                                                              padding: EdgeInsets.only(
                                                                  right: controller
                                                                          .sizedWidth *
                                                                      0.06),
                                                              child: Text(
                                                                  '*  التأرجح : ${controller.addRoutine[r][d]['swing'] == true ? 'يعمل' : 'توقف'}',
                                                                  style:
                                                                      TextStyle(
                                                                    fontSize:
                                                                        controller.sized *
                                                                            0.013,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color: AppColors
                                                                        .textColor2
                                                                        .withOpacity(
                                                                            0.75),
                                                                  )),
                                                            )
                                                          : Container(),
                                                    ],
                                                  ),
                                                )
                                              : controller.rooms[r]['devices']
                                                          [d]['device'] ==
                                                      'TV'
                                                  ? Padding(
                                                      padding: EdgeInsets.only(
                                                          right: controller
                                                                  .sizedWidth *
                                                              0.06),
                                                      child: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .start,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                              '- تلفاز : ' +
                                                                  (controller.rooms[r]['devices'][d]['priv']
                                                                              .toString() ==
                                                                          'x'
                                                                      ? 'لا يوجد اسم'
                                                                      : controller
                                                                          .rooms[
                                                                              r]
                                                                              [
                                                                              'devices']
                                                                              [
                                                                              d]
                                                                              [
                                                                              'priv']
                                                                          .toString()),
                                                              style: TextStyle(
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.013,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.75),
                                                              )),
                                                          Padding(
                                                            padding: EdgeInsets.only(
                                                                right: controller
                                                                        .sizedWidth *
                                                                    0.06),
                                                            child: Text(
                                                                controller.addRoutine[r][d]
                                                                            [
                                                                            'state'] ==
                                                                        true
                                                                    ? '*  تشغيل التلفاز'
                                                                    : '*  اغلاق التلفاز',
                                                                style:
                                                                    TextStyle(
                                                                  fontSize:
                                                                      controller
                                                                              .sized *
                                                                          0.013,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  color: AppColors
                                                                      .textColor2
                                                                      .withOpacity(
                                                                          0.75),
                                                                )),
                                                          ),
                                                          controller.addRoutine[r]
                                                                              [
                                                                              d]
                                                                          [
                                                                          'state'] ==
                                                                      true &&
                                                                  controller.addRoutine[r]
                                                                              [
                                                                              d]
                                                                          [
                                                                          'ch'] !=
                                                                      null
                                                              ? Padding(
                                                                  padding: EdgeInsets.only(
                                                                      right: controller
                                                                              .sizedWidth *
                                                                          0.06),
                                                                  child: Text(
                                                                      '*  القناة : ${controller.addRoutine[r][d]['ch'].toString()}',
                                                                      style:
                                                                          TextStyle(
                                                                        fontSize:
                                                                            controller.sized *
                                                                                0.013,
                                                                        fontWeight:
                                                                            FontWeight.bold,
                                                                        color: AppColors
                                                                            .textColor2
                                                                            .withOpacity(0.75),
                                                                      )),
                                                                )
                                                              : Container(),
                                                          controller.addRoutine[r]
                                                                              [
                                                                              d]
                                                                          [
                                                                          'state'] ==
                                                                      true &&
                                                                  controller.addRoutine[r]
                                                                              [
                                                                              d]
                                                                          [
                                                                          'v'] !=
                                                                      null
                                                              ? Padding(
                                                                  padding: EdgeInsets.only(
                                                                      right: controller
                                                                              .sizedWidth *
                                                                          0.06),
                                                                  child: Text(
                                                                      '*  الصوت :  ${controller.addRoutine[r][d]['v'] == true ? 'كتم الصوت' : controller.addRoutine[r][d]['v'] == false ? 'اللغاء كتم الصوت' : controller.addRoutine[r][d]['v']}',
                                                                      style:
                                                                          TextStyle(
                                                                        fontSize:
                                                                            controller.sized *
                                                                                0.013,
                                                                        fontWeight:
                                                                            FontWeight.bold,
                                                                        color: AppColors
                                                                            .textColor2
                                                                            .withOpacity(0.75),
                                                                      )),
                                                                )
                                                              : Container(),
                                                        ],
                                                      ),
                                                    )
                                                  : Padding(
                                                      padding: EdgeInsets.only(
                                                          right: controller
                                                                  .sizedWidth *
                                                              0.06),
                                                      child: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .start,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                              '- مفاتيح : ' +
                                                                  (controller.rooms[r]['devices'][d]['priv']
                                                                              .toString() ==
                                                                          'x'
                                                                      ? 'لا يوجد اسم'
                                                                      : controller
                                                                          .rooms[
                                                                              r]
                                                                              [
                                                                              'devices']
                                                                              [
                                                                              d]
                                                                              [
                                                                              'priv']
                                                                          .split(
                                                                              '_')[0]),
                                                              style: TextStyle(
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.013,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.75),
                                                              )),
                                                          for (var v
                                                              in controller
                                                                  .addRoutine[r]
                                                                      [d]
                                                                  .keys
                                                                  .toList())
                                                            controller.addRoutine[
                                                                            r][
                                                                        d][v] !=
                                                                    null
                                                                ? Padding(
                                                                    padding: EdgeInsets.only(
                                                                        right: controller.sizedWidth *
                                                                            0.06),
                                                                    child:
                                                                        Column(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .start,
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        Text(
                                                                            '°  ${v}',
                                                                            style:
                                                                                TextStyle(
                                                                              fontSize: controller.sized * 0.013,
                                                                              fontWeight: FontWeight.bold,
                                                                              color: AppColors.textColor2.withOpacity(0.75),
                                                                            )),
                                                                        Padding(
                                                                          padding:
                                                                              EdgeInsets.only(right: controller.sizedWidth * 0.06),
                                                                          child: Text(
                                                                              '*  ${controller.rooms[r]['devices'][d]['priv'].toString() == 'x' ? 'لا يوجد اسم' : controller.rooms[r]['devices'][d]['priv'].split('_')[int.parse(v.toString().replaceFirst('v', ''))]} : ${controller.addRoutine[r][d][v] == true ? 'يعمل' : 'لا يعمل'}',
                                                                              style: TextStyle(
                                                                                fontSize: controller.sized * 0.013,
                                                                                fontWeight: FontWeight.bold,
                                                                                color: AppColors.textColor2.withOpacity(0.75),
                                                                              )),
                                                                        )
                                                                      ],
                                                                    ),
                                                                  )
                                                                : Container(),
                                                        ],
                                                      ),
                                                    ),
                                        Divider(
                                          color: AppColors.textColor2
                                              .withOpacity(0.5),
                                          endIndent: 2,
                                          indent: 2,
                                        )
                                      ],
                                    ),
                                SizedBox(
                                  height: controller.sizedHight * 0.01,
                                )
                              ],
                            ),
                          ),
                        ),
                ]),
          ),
          SizedBox(
            height: controller.sizedHight * 0.025,
          ),
          add
              ? Row(children: [
                  Padding(
                    padding: EdgeInsets.only(
                        right: controller.sizedWidth * 0.03,
                        bottom: controller.sizedHight * 0.01),
                    child: txtStyle(
                        txt: 'اختر الروتين',
                        align: TextAlign.start,
                        color: AppColors.textColor2.withOpacity(0.5),
                        size: controller.sized * 0.012),
                  ),
                ])
              : Container(),
          add || isShortcut
              ? containerPageOption(
                  content: add
                      ? ExpansionTileCard(
                          onExpansionChanged: (shortCut) {
                            setState(() {
                              isShortcut = shortCut;
                            });
                          },
                          initiallyExpanded: isShortcut ? true : false,
                          duration: Duration(milliseconds: 400),
                          baseColor: Colors.transparent,
                          expandedColor: Colors.transparent,
                          borderRadius: BorderRadius.all(Radius.circular(17)),
                          shadowColor: Colors.transparent,
                          contentPadding: EdgeInsets.zero,
                          trailing: Icon(
                            isShortcut
                                ? Icons.check_box_rounded
                                : Icons.check_box_outline_blank_rounded,
                            color: isShortcut
                                ? AppColors.primaryColor
                                : AppColors.textColor2.withOpacity(0.5),
                            size: controller.sized * 0.027,
                          ),
                          title: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              iconStyle(
                                  icon: Icons.shortcut,
                                  color: AppColors.warningColor),
                              SizedBox(
                                width: controller.sizedWidth * 0.02,
                              ),
                              Expanded(
                                child: txtStyle(
                                  align: TextAlign.start,
                                  txt: 'اضافة الى الاختصارات',
                                ),
                              ),
                            ],
                          ),
                          children: [
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: controller.sizedWidth * 0.02),
                                child: Divider(
                                  color: AppColors.textColor2.withOpacity(0.3),
                                  endIndent: 2,
                                  indent: 2,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: controller.sizedWidth * 0.01),
                                child: Row(
                                  children: [
                                    Container(
                                        height: controller.sizedHight * 0.063,
                                        width: controller.sizedWidth * 0.125,
                                        decoration: BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(10)),
                                            border: Border.all(
                                                color: Colors.amber, width: 2)),
                                        child: Center(
                                            child: IconButton(
                                                onPressed: () {
                                                  var setState1 = setState;
                                                  AwesomeDialog(
                                                      context: context,
                                                      dialogType:
                                                          DialogType.noHeader,
                                                      headerAnimationLoop: true,
                                                      animType:
                                                          AnimType.topSlide,
                                                      dialogBackgroundColor:
                                                          AppColors
                                                              .backgroundColor2,
                                                      width:
                                                          controller.sizedWidth,
                                                      body: GetBuilder<
                                                              HomeController>(
                                                          builder:
                                                              (controller) =>
                                                                  StatefulBuilder(
                                                                    builder: (context,
                                                                            setState) =>
                                                                        Center(
                                                                            child:
                                                                                Container(
                                                                      margin: EdgeInsets.symmetric(
                                                                          vertical:
                                                                              controller.sizedHight * 0.01),
                                                                      child:
                                                                          Column(
                                                                        children: [
                                                                          Text(
                                                                            'اختيار ايقونة للاختصار',
                                                                            style: TextStyle(
                                                                                color: AppColors.textColor2.withOpacity(0.5),
                                                                                fontSize: controller.sized * 0.015,
                                                                                fontWeight: FontWeight.bold),
                                                                          ),
                                                                          GridView(
                                                                            padding:
                                                                                const EdgeInsets.only(top: 5),
                                                                            shrinkWrap:
                                                                                true,
                                                                            physics:
                                                                                const NeverScrollableScrollPhysics(),
                                                                            gridDelegate:
                                                                                const SliverGridDelegateWithFixedCrossAxisCount(
                                                                              crossAxisCount: 4,
                                                                              childAspectRatio: 0.85,
                                                                            ),
                                                                            children: [
                                                                              for (String i in routineIcons.keys.toList())
                                                                                IconButton(
                                                                                  onPressed: () {
                                                                                    setState1(
                                                                                      () {
                                                                                        routineIcon = i;
                                                                                      },
                                                                                    );
                                                                                    if (Navigator.of(context).canPop()) {
                                                                                      Navigator.of(context).pop();
                                                                                    }
                                                                                  },
                                                                                  icon: Icon(
                                                                                    routineIcons[i],
                                                                                    size: controller.sized * 0.03,
                                                                                  ),
                                                                                  color: Colors.amber,
                                                                                )
                                                                            ],
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    )),
                                                                  ))).show();
                                                },
                                                icon: Icon(
                                                  routineIcons[routineIcon],
                                                  color: Colors.amber,
                                                  size:
                                                      controller.sized * 0.026,
                                                )))),
                                    SizedBox(
                                      width: controller.sizedWidth * 0.02,
                                    ),
                                    Form(
                                      key: kname1,
                                      child: Flexible(
                                        child: TextFormField(
                                          controller: name1,
                                          validator: (val) {
                                            for (var i = 0;
                                                i < name1.text.length;
                                                i++) {
                                              if (arabic.contains(
                                                      name1.text[i]) ||
                                                  name1.text[i].isNumericOnly) {
                                                edit = true;
                                              } else {
                                                return 'قم بادخال حروف عربية او ارقام فقط';
                                              }
                                            }

                                            return null;
                                          },
                                          maxLength: 25,
                                          showCursor: true,
                                          cursorColor: AppColors.textColor,
                                          // textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                            color: AppColors.textColor
                                                .withOpacity(0.6),
                                            fontSize: controller.sized * 0.012,
                                            fontWeight: FontWeight.bold,
                                          ),

                                          onEditingComplete: () {
                                            FocusManager.instance.primaryFocus
                                                ?.unfocus();
                                            if (kname1.currentState != null) {
                                              var formdata =
                                                  kname1.currentState;
                                              formdata!.validate();
                                            }
                                          },
                                          decoration: InputDecoration(
                                            hintText: 'اسم الاختصار - اختياري',
                                            hintStyle: TextStyle(
                                                color: AppColors.textColor2
                                                    .withOpacity(0.5)),
                                            focusedBorder: UnderlineInputBorder(
                                                borderSide: BorderSide(
                                                    color: AppColors.textColor2
                                                        .withOpacity(0.5))),
                                            suffixIcon: Icon(
                                              Icons.edit_rounded,
                                              color: AppColors.textColor2
                                                  .withOpacity(0.5),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ])
                      : Row(
                          children: [
                            Container(
                                height: controller.sizedHight * 0.063,
                                width: controller.sizedWidth * 0.125,
                                decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10)),
                                    border: Border.all(
                                        color: Colors.amber, width: 2)),
                                child: Center(
                                    child: IconButton(
                                        onPressed: () {
                                          var setState1 = setState;
                                          AwesomeDialog(
                                              context: context,
                                              dialogType: DialogType.noHeader,
                                              headerAnimationLoop: true,
                                              animType: AnimType.topSlide,
                                              dialogBackgroundColor:
                                                  AppColors.backgroundColor2,
                                              width: controller.sizedWidth,
                                              body: GetBuilder<HomeController>(
                                                  builder:
                                                      (controller) =>
                                                          StatefulBuilder(
                                                            builder: (context,
                                                                    setState) =>
                                                                Center(
                                                                    child:
                                                                        Container(
                                                              margin: EdgeInsets
                                                                  .symmetric(
                                                                      vertical:
                                                                          controller.sizedHight *
                                                                              0.01),
                                                              child: Column(
                                                                children: [
                                                                  Text(
                                                                    'اختيار ايقونة للاختصار',
                                                                    style: TextStyle(
                                                                        color: AppColors
                                                                            .textColor2
                                                                            .withOpacity(
                                                                                0.5),
                                                                        fontSize:
                                                                            controller.sized *
                                                                                0.015,
                                                                        fontWeight:
                                                                            FontWeight.bold),
                                                                  ),
                                                                  GridView(
                                                                    padding: const EdgeInsets
                                                                        .only(
                                                                        top: 5),
                                                                    shrinkWrap:
                                                                        true,
                                                                    physics:
                                                                        const NeverScrollableScrollPhysics(),
                                                                    gridDelegate:
                                                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                                                      crossAxisCount:
                                                                          4,
                                                                      childAspectRatio:
                                                                          0.85,
                                                                    ),
                                                                    children: [
                                                                      for (String i in routineIcons
                                                                          .keys
                                                                          .toList())
                                                                        IconButton(
                                                                          onPressed:
                                                                              () {
                                                                            setState1(
                                                                              () {
                                                                                routineIcon = i;
                                                                              },
                                                                            );
                                                                            if (Navigator.of(context).canPop()) {
                                                                              Navigator.of(context).pop();
                                                                            }
                                                                          },
                                                                          icon:
                                                                              Icon(
                                                                            routineIcons[i],
                                                                            size:
                                                                                controller.sized * 0.03,
                                                                          ),
                                                                          color:
                                                                              Colors.amber,
                                                                        )
                                                                    ],
                                                                  ),
                                                                ],
                                                              ),
                                                            )),
                                                          ))).show();
                                        },
                                        icon: Icon(
                                          routineIcons[routineIcon],
                                          color: Colors.amber,
                                          size: controller.sized * 0.026,
                                        )))),
                            SizedBox(
                              width: controller.sizedWidth * 0.02,
                            ),
                            Form(
                              key: kname1,
                              child: Flexible(
                                child: TextFormField(
                                  controller: name1,
                                  validator: (val) {
                                    for (var i = 0;
                                        i < name1.text.length;
                                        i++) {
                                      if (arabic.contains(name1.text[i]) ||
                                          name1.text[i].isNumericOnly) {
                                        edit = true;
                                      } else {
                                        return 'قم بادخال حروف عربية او ارقام فقط';
                                      }
                                    }

                                    return null;
                                  },
                                  maxLength: 25,
                                  showCursor: true,
                                  cursorColor: AppColors.textColor,
                                  // textDirection: TextDirection.rtl,
                                  style: TextStyle(
                                    color: AppColors.textColor.withOpacity(0.6),
                                    fontSize: controller.sized * 0.015,
                                    fontWeight: FontWeight.bold,
                                  ),

                                  onEditingComplete: () {
                                    FocusManager.instance.primaryFocus
                                        ?.unfocus();
                                    if (kname1.currentState != null) {
                                      var formdata = kname1.currentState;
                                      formdata!.validate();
                                    }
                                  },
                                  decoration: InputDecoration(
                                    hintText: 'اسم الاختصار - اختياري',
                                    hintStyle: TextStyle(
                                        color: AppColors.textColor2
                                            .withOpacity(0.5)),
                                    focusedBorder: UnderlineInputBorder(
                                        borderSide: BorderSide(
                                            color: AppColors.textColor2
                                                .withOpacity(0.5))),
                                    suffixIcon: Icon(
                                      Icons.edit_rounded,
                                      color:
                                          AppColors.textColor2.withOpacity(0.5),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                )
              : Container(),
          SizedBox(
            height: controller.sizedHight * 0.02,
          ),
          add || isWords
              ? containerPageOption(
                  content: ExpansionTileCard(
                      onExpansionChanged: (words) {
                        setState(() {
                          isWords = words;
                        });
                      },
                      initiallyExpanded: isWords ? true : false,
                      duration: Duration(milliseconds: 400),
                      baseColor: Colors.transparent,
                      expandedColor: Colors.transparent,
                      borderRadius: BorderRadius.all(Radius.circular(17)),
                      shadowColor: Colors.transparent,
                      contentPadding: EdgeInsets.zero,
                      trailing: Icon(
                        isWords
                            ? Icons.check_box_rounded
                            : Icons.check_box_outline_blank_rounded,
                        color: isWords
                            ? AppColors.primaryColor
                            : AppColors.textColor2.withOpacity(0.5),
                        size: controller.sized * 0.027,
                      ),
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          iconStyle(
                              icon: Icons.add_comment,
                              color: AppColors.warningColor,
                              size: controller.sized * 0.025),
                          SizedBox(
                            width: controller.sizedWidth * 0.03,
                          ),
                          Expanded(
                            child: txtStyle(
                              align: TextAlign.start,
                              txt: 'اضافة الى الكلمات الروتينية',
                            ),
                          ),
                        ],
                      ),
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          child: Divider(
                            color: AppColors.textColor2.withOpacity(0.3),
                            endIndent: 2,
                            indent: 2,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          child: Form(
                            key: kname2,
                            child: Container(
                              width: controller.sizedWidth * 0.85,
                              child: TextFormField(
                                controller: name2,
                                validator: (val) {
                                  if (isWords && (val == null || val.isEmpty)) {
                                    return 'يجب عليك ملء هذه الخانة';
                                  }
                                  for (var i = 0; i < name2.text.length; i++) {
                                    if (arabic.contains(name2.text[i]) ||
                                        name2.text[i].isNumericOnly) {
                                      edit = true;
                                    } else {
                                      return 'قم بادخال حروف عربية او ارقام فقط';
                                    }
                                  }
                                  for (var word in controller.routineWords!) {
                                    if (editChar(word.fields['word']) ==
                                        editChar(name2.text)) {
                                      return 'هذه الجمله مستخدمه في الكلمات الروتينيه';
                                    }
                                  }

                                  return null;
                                },
                                maxLength: 25,
                                showCursor: true,
                                cursorColor: AppColors.primary,
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                  color: AppColors.textPrimary,
                                  fontSize: controller.sized * 0.012,
                                  fontWeight: FontWeight.w500,
                                ),
                                onEditingComplete: () {
                                  FocusManager.instance.primaryFocus?.unfocus();
                                  if (kname2.currentState != null) {
                                    var formdata = kname2.currentState;
                                    formdata!.validate();
                                  }
                                },
                                decoration: InputDecoration(
                                  hintText: 'مثلاً: صباح الخير',
                                  hintStyle: TextStyle(
                                    color: AppColors.textHint,
                                    fontSize: controller.sized * 0.011,
                                    fontWeight: FontWeight.normal,
                                  ),
                                  filled: true,
                                  fillColor: AppColors.surface,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: controller.sizedWidth * 0.04,
                                    vertical: controller.sizedHight * 0.015,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.border,
                                      width: 1.0,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.border,
                                      width: 1.0,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.primary,
                                      width: 2.0,
                                    ),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.error,
                                      width: 1.5,
                                    ),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(
                                      color: AppColors.error,
                                      width: 2.0,
                                    ),
                                  ),
                                  suffixIcon: Icon(
                                    Icons.edit_rounded,
                                    size: controller.sized * 0.017,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ]),
                )
              : Container(),
          SizedBox(
            height: controller.sizedHight * 0.02,
          ),
          add || isTask
              ? containerPageOption(
                  content: MaterialButton(
                  padding: EdgeInsets.symmetric(
                      vertical: controller.sizedHight * 0.02),
                  onPressed: () async {
                    var setState1 = setState;
                    await alarm(
                        txt: 'قائمة المهام',
                        context: context,
                        setState1: setState1,
                        submit: () {
                          setState(
                            () {
                              isScheduler = true;
                            },
                          );
                        },
                        del: () {
                          setState(
                            () {
                              isScheduler = false;
                            },
                          );
                        });
                    2.delay();
                    setState1(
                      () {
                        isScheduler;
                        isTask = isScheduler;
                      },
                    );
                  },
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          iconStyle(
                              icon: Icons.more_time_rounded,
                              color: AppColors.warningColor,
                              size: controller.sized * 0.025),
                          SizedBox(
                            width: controller.sizedWidth * 0.02,
                          ),
                          Expanded(
                            child: txtStyle(
                              align: TextAlign.start,
                              txt: "الجدولة الى قائمة المهام",
                            ),
                          ),
                          SizedBox(
                            width: controller.sizedWidth * 0.05,
                          ),
                          Icon(
                            isTask
                                ? Icons.check_box_rounded
                                : Icons.check_box_outline_blank_rounded,
                            color: isTask
                                ? AppColors.primaryColor
                                : AppColors.textColor2.withOpacity(0.5),
                            size: controller.sized * 0.027,
                          ),
                        ],
                      ),
                      isTask
                          ? Padding(
                              padding: EdgeInsets.only(
                                  right: controller.sizedWidth * 0.02,
                                  left: controller.sizedWidth * 0.02,
                                  top: controller.sizedHight * 0.02),
                              child: Divider(
                                color: AppColors.textColor2.withOpacity(0.3),
                                endIndent: 2,
                                indent: 2,
                              ),
                            )
                          : Container(),
                      isTask && isScheduler == false
                          ? txtStyle(
                              txt: 'إضغط لإختيار موعد للروتين',
                              color: AppColors.errorColor)
                          : isTask && isScheduler
                              ? Directionality(
                                  textDirection: TextDirection.rtl,
                                  child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal:
                                              controller.sizedWidth * 0.02),
                                      child: Column(
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Text(
                                                h.toString() +
                                                    ':' +
                                                    ([
                                                      0,
                                                      1,
                                                      2,
                                                      3,
                                                      4,
                                                      5,
                                                      6,
                                                      7,
                                                      8,
                                                      9
                                                    ].contains(m)
                                                        ? '0' + m.toString()
                                                        : m.toString()),
                                                textDirection:
                                                    TextDirection.rtl,
                                                style: TextStyle(
                                                  color: AppColors.warningColor,
                                                  fontSize:
                                                      controller.sized * 0.035,
                                                ),
                                              ),
                                              SizedBox(
                                                width: controller.sizedWidth *
                                                    0.005,
                                              ),
                                              Padding(
                                                padding: EdgeInsets.only(
                                                    bottom:
                                                        controller.sizedHight *
                                                            0.008,
                                                    right: controller.sized *
                                                        0.001),
                                                child: Text(
                                                  isAM ? 'صباحاً' : 'مسائاً',
                                                  textAlign: TextAlign.justify,
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color:
                                                          AppColors.textColor2,
                                                      fontSize:
                                                          controller.sized *
                                                              0.014,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                              ),
                                            ],
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal:
                                                    controller.sizedWidth *
                                                        0.01),
                                            child: Row(
                                              children: [
                                                Text(
                                                  'سبت',
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color: days
                                                              .contains('سبت')
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textColor2
                                                              .withOpacity(0.2),
                                                      fontSize:
                                                          controller.sized *
                                                              0.01,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: controller.sizedWidth *
                                                      0.02,
                                                ),
                                                Text(
                                                  'أحد',
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color: days
                                                              .contains("أحد")
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textColor2
                                                              .withOpacity(0.2),
                                                      fontSize:
                                                          controller.sized *
                                                              0.01,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: controller.sizedWidth *
                                                      0.02,
                                                ),
                                                Text(
                                                  'إثنين',
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color: days
                                                              .contains("إثنين")
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textColor2
                                                              .withOpacity(0.2),
                                                      fontSize:
                                                          controller.sized *
                                                              0.01,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: controller.sizedWidth *
                                                      0.02,
                                                ),
                                                Text(
                                                  'ثلاثاء',
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color: days.contains(
                                                              "ثلاثاء")
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textColor2
                                                              .withOpacity(0.2),
                                                      fontSize:
                                                          controller.sized *
                                                              0.01,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: controller.sizedWidth *
                                                      0.02,
                                                ),
                                                Text(
                                                  'اربعاء',
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color: days.contains(
                                                              "اربعاء")
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textColor2
                                                              .withOpacity(0.2),
                                                      fontSize:
                                                          controller.sized *
                                                              0.01,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: controller.sizedWidth *
                                                      0.02,
                                                ),
                                                Text(
                                                  'خميس',
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color: days
                                                              .contains("خميس")
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textColor2
                                                              .withOpacity(0.2),
                                                      fontSize:
                                                          controller.sized *
                                                              0.01,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: controller.sizedWidth *
                                                      0.02,
                                                ),
                                                Text(
                                                  'جمعة',
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  style: TextStyle(
                                                      color: days
                                                              .contains("جمعة")
                                                          ? AppColors
                                                              .warningColor
                                                          : AppColors.textColor2
                                                              .withOpacity(0.2),
                                                      fontSize:
                                                          controller.sized *
                                                              0.01,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(
                                                  width: controller.sizedWidth *
                                                      0.05,
                                                ),
                                                Icon(
                                                  Icons.refresh_rounded,
                                                  size:
                                                      controller.sized * 0.018,
                                                  color: re
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                )
                                              ],
                                            ),
                                          )
                                        ],
                                      )),
                                )
                              : Container()
                    ],
                  ),
                ))
              : Container(),
          SizedBox(
            height: controller.sizedHight * 0.02,
          ),
          Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                submitButtom(
                  text: add ? "خروج" : "تعديل",
                  onPressed: () async {
                    if (isShortcut) {
                      if (kname1.currentState != null) {
                        var formdata = kname1.currentState;
                        if (formdata!.validate() != true) {
                          return;
                        }
                      }

                      FocusManager.instance.primaryFocus?.unfocus();
                      print(name1.text);
                      print(routineIcon);

                      print('55fsdgsdg555555555555555555555');

                      var appDB = await openDatabase('${controller.system}.db',
                          version: 3);
                      if (add) {
                        appDB.rawQuery(
                            'insert into routine(name,routine,icon) values(?,?,?)',
                            [
                              name1.text.isEmpty ? 'None' : name1.text,
                              json.encode(controller.addRoutine),
                              routineIcon
                            ]);
                      } else {
                        await appDB.transaction((txn) async {
                          print(12345);
                          await txn.rawUpdate(
                              'UPDATE routine SET name = ?, routine = ?, icon = ? WHERE id = ?',
                              [
                                name1.text.isEmpty ? 'None' : name1.text,
                                json.encode(controller.addRoutine),
                                routineIcon,
                                myId
                              ]);
                          print(123456);
                        });
                      }
                      getDevices();
                      if (isSetting == false) {}
                    }
                    if (isWords) {
                      if (kname2.currentState != null) {
                        var formdata = kname2.currentState;
                        if (formdata!.validate() != true) {
                          if (isShortcut) {
                            isShortcut = false;
                          }
                          return;
                        }
                      }
                      final conn =
                          await MySqlConnection.connect(ConnectionSettings(
                              host: controller.hostZain.value,
                              // port: 80,
                              user: 'root',
                              db: 'zain',
                              password: 'zain',
                              characterSet: CharacterSet.UTF8));

                      if (add) {
                        await conn.query(
                            "INSERT INTO RDevice(route,word) values(?,?)", [
                          json.encode(controller.addRoutine),
                          name2.text,
                        ]);
                      } else {
                        await conn.query(
                            "UPDATE RDevice SET route = ?, word = ? WHERE id = ?",
                            [
                              json.encode(controller.addRoutine),
                              name2.text,
                              myId
                            ]);
                      }
                      conn.close();
                      await getDevices();
                    }
                    if (isTask && isScheduler) {
                      final conn =
                          await MySqlConnection.connect(ConnectionSettings(
                              host: controller.hostZain.value,
                              // port: 80,
                              user: 'root',
                              db: 'zain',
                              password: 'zain',
                              characterSet: CharacterSet.UTF8));
                      String wdays = '';
                      if (days.isEmpty) {
                        wdays = 'None';
                      } else {
                        for (var i in days) {
                          wdays += weekDays[i] + ' ';
                        }
                        wdays = wdays.substring(0, wdays.length - 1);
                      }
                      print(wdays);
                      String clock = h.toString() +
                          ':' +
                          (m > 9 ? m.toString() : '0' + m.toString());
                      String time = isAM ? 'AM' : 'PM';
                      String ree = re ? 'ON' : 'OFF';
                      if (add) {
                        await conn.query(
                            "INSERT INTO ADevice(route,wday,nclock,re,state) values(?,?,?,?,?)",
                            [
                              json.encode(controller.addRoutine),
                              wdays,
                              '$clock $time',
                              ree,
                              'ON'
                            ]);
                      } else {
                        await conn.query(
                            "UPDATE ADevice SET route = ?, topic = ?, command = ?, wday = ?, nclock = ?, re = ?, state = ? WHERE id = ?",
                            [
                              json.encode(controller.addRoutine),
                              null,
                              null,
                              wdays,
                              '$clock $time',
                              ree,
                              'ON',
                              myId
                            ]);
                      }
                      conn.close();
                      await getDevices();
                    }
                    if (isTask && isScheduler && isSetting) {
                      setState(() {
                        p = 4;
                        controller.addRoutine.clear();
                        isSetting = true;
                        isShortcut = false;
                        isTask = true;
                        isWords = false;
                        add = true;
                        myId = '';
                      });

                      pageController.jumpToPage(p);
                      tasks(context, setState);
                    } else if (isWords && isSetting) {
                      setState(() {
                        p = 4;
                        controller.addRoutine.clear();
                        isSetting = true;
                        isShortcut = false;
                        isTask = false;
                        isWords = true;
                        add = true;
                        myId = '';
                      });

                      pageController.jumpToPage(p);
                      routineWords(context, setState);
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                ),
                add == false
                    ? Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: controller.sizedWidth * 0.02),
                        child: delButtom(
                          text: 'حذف',
                          onPressed: () async {
                            var formdata = kname1.currentState;
                            var formdata2 = kname2.currentState;
                            if (isShortcut && formdata!.validate()) {
                              FocusManager.instance.primaryFocus?.unfocus();
                              var appDB = await openDatabase(
                                  '${controller.system}.db',
                                  version: 3);
                              await appDB.rawQuery(
                                  'DELETE FROM routine WHERE id = ${myId}');
                              getDevices();
                              Navigator.of(context).pop();
                            } else if (isWords) {
                              final conn = await MySqlConnection.connect(
                                  ConnectionSettings(
                                      host: controller.hostZain.value,
                                      // port: 80,
                                      user: 'root',
                                      db: 'zain',
                                      password: 'zain',
                                      characterSet: CharacterSet.UTF8));
                              await conn.query(
                                  "DELETE FROM RDevice WHERE id = ?", [myId]);
                              setState(() {
                                p = 4;
                                controller.addRoutine.clear();
                                isSetting = true;
                                isShortcut = false;
                                isTask = false;
                                isWords = true;
                                add = true;
                                myId = '';
                              });
                              await getDevices();
                              pageController.jumpToPage(p);
                              routineWords(context, setState);
                            } else if (isScheduler && isTask) {
                              final conn = await MySqlConnection.connect(
                                  ConnectionSettings(
                                      host: controller.hostZain.value,
                                      // port: 80,
                                      user: 'root',
                                      db: 'zain',
                                      password: 'zain',
                                      characterSet: CharacterSet.UTF8));
                              await conn.query(
                                  "DELETE FROM ADevice WHERE id = ?", [myId]);
                              setState(() {
                                p = 4;
                                controller.addRoutine.clear();
                                isSetting = true;
                                isShortcut = false;
                                isTask = true;
                                isWords = false;
                                add = true;
                                myId = '';
                              });
                              await getDevices();
                              pageController.jumpToPage(p);
                              tasks(context, setState);
                            }
                          },
                        ),
                      )
                    : Container()
              ],
            ),
          ),
          SizedBox(
            height: controller.sizedHight * 0.06,
          ),
        ],
      ),
    ),
  );
}
