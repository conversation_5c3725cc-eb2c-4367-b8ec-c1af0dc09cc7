import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

import 'package:percent_indicator/percent_indicator.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/models/pages.dart';
import 'package:zaen/models/shortcuts.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/commands/room.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/commands/home.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/constants.dart';
import 'dashbourd.dart/favorite/favorite.dart';
import 'package:zaen/view/home/<USER>/routine.dart';
import 'package:zaen/view/home/<USER>/setting.dart';
import 'package:zaen/shared/themes/app_colors.dart';

class Home extends StatefulWidget {
  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  // const Home({ Key? key }) : super(key: key);
  var hostZain;

  HomeController controller = Get.put(HomeController(), permanent: true);
  int p = 1;
  bool scroll = false;
  ScrollMetrics? m;
  bool menublur = false;
  bool editFavorite = false;
  // late AnimationController animationController;

  @override
  Widget build(BuildContext context) {
    controller.sizedHight = MediaQuery.of(context).size.height;
    controller.sizedWidth = MediaQuery.of(context).size.width;
    controller.sized = controller.sizedHight + controller.sizedWidth;
    PageController _pageController = PageController(initialPage: p);
    print('tttttttttttttttttttttttttttttttttttttt');
    // animationController = new AnimationController(
    //   vsync: this,
    //   duration: new Duration(milliseconds: 5000),
    // );
    // animationController.forward();
    // animationController.addListener(() {
    //   setState(() {
    //     if (animationController.status == AnimationStatus.completed) {
    //       animationController.repeat();
    //     }
    //   });
    // });

    print(controller.home.value);

    return Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        // backgroundColor: AppColors.textColor,
        body: GetBuilder<HomeController>(
            builder: (controller) => ConditionalBuilder(
                  condition: controller.home.value.isNotEmpty,
                  fallback: (context) {
                    print('fffffffffffffff');
                    return const Center(child: CircularProgressIndicator());
                  },
                  builder: (context) => Stack(
                    children: [
                      controller.homeImage.value.contains('com.example.zaen')
                          ? Image.file(
                              File(controller.homeImage.value),
                              height: MediaQuery.of(context).size.height,
                              width: MediaQuery.of(context).size.width,
                              fit: BoxFit.cover,
                              filterQuality: FilterQuality.high,
                            )
                          : Image.asset(
                              "${controller.homeImage.value}",
                              height: MediaQuery.of(context).size.height,
                              width: MediaQuery.of(context).size.width,
                              filterQuality: FilterQuality.high,

                              // color: AppColors.textColor.withOpacity(0.25),
                              // colorBlendMode: BlendMode.darken,
                              fit: BoxFit.cover,
                            ),
                      BackdropFilter(
                        blendMode: BlendMode.srcIn,
                        filter: ImageFilter.blur(
                          sigmaX: 40,
                          sigmaY: 40,
                        ),
                        child: Container(
                            height: MediaQuery.of(context).size.height,
                            width: MediaQuery.of(context).size.width,
                            color: AppColors.textColor.withOpacity(0.05)),
                      ),
                      NestedScrollView(
                        headerSliverBuilder:
                            (BuildContext context, bool innerBoxIsScrolled) {
                          return <Widget>[
                            Directionality(
                              textDirection: TextDirection.rtl,
                              child: SliverAppBar(
                                expandedHeight: p != 1
                                    ? controller.sizedHight * 0.2
                                    : controller.sizedHight * 0.2,
                                backgroundColor: p != 1
                                    ? AppColors.backgroundColor
                                        .withOpacity(0.65)
                                    : scroll
                                        ? AppColors.backgroundColor
                                            .withOpacity(0.65)
                                        : Colors.transparent,
                                // leadingWidth: double.infinity,
                                actions: [
                                  IconButton(
                                    onPressed: () {
                                      if (controller.homeId.isNotEmpty &&
                                          client.connectionStatus!.state.name ==
                                              'connected') {
                                        isSetting = true;
                                        addRoutine(context: context);
                                      }
                                    },
                                    icon: Icon(
                                      Icons.settings_rounded,
                                    ),
                                    color: AppColors.textColor.withOpacity(0.9),
                                    iconSize: controller.sized * 0.025,
                                  ),
                                  // addRoutine(isSetting: true, context: context),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            controller.sizedWidth * 0.02),
                                    child: controller.devices['home'] ==
                                                false ||
                                            client.connectionStatus!.state
                                                    .name !=
                                                'connected'
                                        ? Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal:
                                                    controller.sizedWidth *
                                                        0.01,
                                                vertical:
                                                    controller.sizedHight *
                                                        0.015),
                                            child: Text(
                                              'غير متصل',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: Color.fromARGB(
                                                      255, 238, 19, 3),
                                                  fontSize:
                                                      controller.sized * 0.0135,
                                                  fontWeight: FontWeight.bold),
                                            ))
                                        : switchStyle(
                                            value: controller.homeState,
                                            onChanged: (val) {
                                              commandHome(val!);
                                            }),
                                  ),
                                ],
                                leading: PopupMenuButton<String>(
                                  onOpened: () {
                                    setState(() {
                                      menublur = true;
                                    });
                                  },
                                  onCanceled: () {
                                    setState(() {
                                      menublur = false;
                                    });
                                  },
                                  padding: EdgeInsets.zero,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15)),
                                  color: AppColors.backgroundColor2
                                      .withOpacity(0.85),
                                  shadowColor: Colors.transparent,
                                  icon: Icon(
                                    Icons.home_work,
                                    color: AppColors.textColor.withOpacity(0.9),
                                    size: controller.sized * 0.027,
                                  ),
                                  offset: Offset(-25, 45),
                                  itemBuilder: (context) {
                                    final menuItems = <PopupMenuEntry<String>>[
                                      PopupMenuItem<String>(
                                        enabled: true,
                                        value: 'new',
                                        height: controller.sizedHight * 0.035,
                                        child: Container(
                                          color: Colors.transparent,
                                          child: Row(
                                            textDirection: TextDirection.rtl,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                'العثور على نظام جديد',
                                                textDirection:
                                                    TextDirection.rtl,
                                                style: TextStyle(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.8),
                                                    fontSize: controller.sized *
                                                        0.012,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              iconStyle(
                                                  icon: Icons.add_home_rounded,
                                                  color: AppColors.warningColor,
                                                  size:
                                                      controller.sized * 0.02),
                                            ],
                                          ),
                                        ),
                                      ),
                                      PopupMenuItem<String>(
                                          enabled: false,
                                          height: 0,
                                          padding: EdgeInsets.zero,
                                          child: Divider(
                                            color: AppColors.textColor
                                                .withOpacity(0.2),
                                          )),
                                      PopupMenuItem<String>(
                                        value: controller.home.value,
                                        height: controller.sizedHight * 0.035,
                                        child: Container(
                                          // constraints: BoxConstraints(
                                          //     minWidth: controller
                                          //             .sizedWidth *
                                          //         0.3), // تحديد العرض الأقصى
                                          child: Row(
                                            textDirection: TextDirection.rtl,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                controller.home.value,
                                                textDirection:
                                                    TextDirection.rtl,
                                                style: TextStyle(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.8),
                                                    fontSize: controller.sized *
                                                        0.012,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              Icon(Icons.check,
                                                  color: AppColors.primaryColor,
                                                  size: 20),
                                            ],
                                          ),
                                        ),
                                      ),
                                      if (controller.systems.length != 1)
                                        PopupMenuItem<String>(
                                            enabled: false,
                                            height: 0,
                                            padding: EdgeInsets.zero,
                                            child: Divider(
                                                color: AppColors.textColor
                                                    .withOpacity(0.2))),
                                    ];
                                    // controller.systems = [
                                    //   {'title': '2'},
                                    //   {'title': '4'},
                                    //   {'title': '6'},
                                    //   {'title': 'سسسسسسسسسسسسسسس'},
                                    //   {'title': '4'},
                                    //   {'title': '6'},
                                    //   {'title': 'المعرض'},
                                    //   {'title': '4'},
                                    //   {'title': '6'},
                                    // ];
                                    for (int i = 0;
                                        i < controller.systems.length;
                                        i++) {
                                      final house = controller.systems[i];

                                      // Add menu item
                                      if (house['title'] !=
                                          controller.home.value) {
                                        menuItems.add(
                                          PopupMenuItem<String>(
                                            value: house['title'],
                                            height:
                                                controller.sizedHight * 0.03,
                                            child: Container(
                                              // constraints: BoxConstraints(
                                              //     minWidth: controller
                                              //             .sizedWidth *
                                              //         0.3), // تحديد العرض الأقصى
                                              child: Row(
                                                textDirection:
                                                    TextDirection.rtl,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    house['title'].toString(),
                                                    textDirection:
                                                        TextDirection.rtl,
                                                    style: TextStyle(
                                                        color: AppColors
                                                            .textColor
                                                            .withOpacity(0.5),
                                                        fontSize:
                                                            controller.sized *
                                                                0.012,
                                                        fontWeight:
                                                            FontWeight.bold),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );

                                        // Add divider if not the last item
                                        if (i < controller.systems.length - 1) {
                                          menuItems.add(
                                            PopupMenuItem<String>(
                                                enabled: false,
                                                height: 0,
                                                padding: EdgeInsets.zero,
                                                child: Divider(
                                                  color: AppColors.textColor
                                                      .withOpacity(0.2),
                                                )),
                                          );
                                        }
                                      }
                                    }

                                    return menuItems;
                                  },
                                  onSelected: (value) async {
                                    print(controller.tasks);
                                    setState(() {
                                      menublur = false;
                                    });
                                    if (value == 'new') {
                                      Get.toNamed('wait');
                                    } else if (value != 'x') {
                                      print('x');
                                    }
                                  },
                                  constraints: BoxConstraints(
                                    maxHeight: controller.sizedHight *
                                        0.3, // ارتفاع القائمة
                                    minWidth: controller.sizedWidth * 0.6,
                                    maxWidth: controller.sizedWidth *
                                        0.6, // عرض القائمة
                                  ),
                                ),
                                flexibleSpace: NotificationListener(
                                  child: ClipRect(
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(
                                        sigmaX: p == 0 || scroll ? 75 : 0,
                                        sigmaY: p == 0 || scroll ? 75 : 0,
                                      ),
                                      child: FlexibleSpaceBar(
                                        titlePadding: EdgeInsets.symmetric(
                                            vertical:
                                                controller.sizedHight * 0.015),
                                        centerTitle: true,
                                        expandedTitleScale:
                                            controller.sized * 0.0015,
                                        title: Text(
                                          controller.home.value,
                                          style: TextStyle(
                                              color: AppColors.textPrimary
                                                  .withOpacity(0.9),
                                              fontSize: controller.sized * 0.02,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        background: p == 1
                                            ? Container()
                                            : controller.homeImage.value
                                                    .contains(
                                                        'com.example.zaen')
                                                ? Image.file(
                                                    File(controller
                                                        .homeImage.value),
                                                    color: AppColors
                                                        .subtitleColor
                                                        .withOpacity(0.3),
                                                    colorBlendMode:
                                                        BlendMode.darken,
                                                    fit: BoxFit.cover,
                                                    filterQuality:
                                                        FilterQuality.high,
                                                  )
                                                : Image.asset(
                                                    "${controller.homeImage.value}",
                                                    color: AppColors
                                                        .subtitleColor
                                                        .withOpacity(0.3),
                                                    colorBlendMode:
                                                        BlendMode.darken,
                                                    fit: BoxFit.cover,
                                                    filterQuality:
                                                        FilterQuality.high,
                                                  ),
                                      ),
                                    ),
                                  ),
                                ),
                                pinned: true,
                                //Whether the app bar should remain visible at the start of the scroll view
                                floating: false,
                                //Whether the app bar should become visible as soon as the user scrolls towards the app bar.
                                snap: false,
                              ),
                            )
                          ];
                        },
                        body: ConditionalBuilder(
                          condition: controller.homeId.isNotEmpty &&
                              client.connectionStatus!.state.name ==
                                  'connected',
                          fallback: (context) {
                            // print(controller.hostZain);
                            // print(controller.homeId);
                            // print(111111111111111);
                            // final info = NetworkInfo();
                            // final wifiName = info.getWifiName(); // "FooNetwork"
                            // final wifiBSSID =
                            //     info.getWifiBSSID(); // 11:22:33:44:55:66
                            // final wifiIP = info.getWifiGatewayIP();
                            // if (!controller.reg &&
                            //     wifiName != null &&
                            //     controller.hostZain.value.isNotEmpty &&
                            //     wifiName.toString().toUpperCase().contains(
                            //         wifiBSSID.toString().toUpperCase()) &&
                            //     wifiIP == '***********') {
                            //   controller.reg = true;
                            //   controller.apMAC = wifiBSSID.toString();
                            //   controller.apName =
                            //       wifiName.toString().split('/')[0];
                            //   findIp();
                            // }
                            return const Center(
                                child: CircularProgressIndicator());
                          },
                          builder: (context) {
                            client.subscribe(controller.homeId + "/app/phone",
                                MqttQos.atLeastOnce);
                            client.subscribe("edit", MqttQos.atLeastOnce);
                            client.subscribe(
                                controller.uuid, MqttQos.atLeastOnce);
                            return NotificationListener<
                                ScrollMetricsNotification>(
                              onNotification:
                                  (ScrollMetricsNotification notification) {
                                int t;
                                int s;
                                checkScroll() async {
                                  if (m == null) {
                                    m = notification.metrics;
                                  }

                                  if (p == 1 &&
                                      notification.metrics.axis ==
                                          Axis.vertical) {
                                    t = await m!.maxScrollExtent.toInt() - 75;
                                    s = await notification
                                        .metrics.maxScrollExtent
                                        .toInt();
                                    print(t.toString() + ' ' + s.toString());
                                    print(t > s);
                                    if (t > s) {
                                      scroll = true;
                                    } else {
                                      scroll = false;
                                    }
                                  }
                                }

                                setState(() {
                                  checkScroll();
                                });

                                return true;
                              },
                              child: PageView(
                                  onPageChanged: ((value) {
                                    setState(() {
                                      p = value;
                                      scroll;
                                    });
                                  }),
                                  controller: _pageController,
                                  children: [
                                    SingleChildScrollView(
                                      physics: BouncingScrollPhysics(),
                                      child: Column(
                                        children: [
                                          for (dynamic i
                                              in controller.rooms.values)
                                            (shortcutRoom(
                                              sizedWidth: controller.sizedWidth,
                                              sizedHeight:
                                                  controller.sizedHight,
                                              sized: controller.sized,
                                              connect:
                                                  controller.devices[i['id']],
                                              onTap: () {
                                                controller.roomData = i;
                                                Get.toNamed('room');
                                              },
                                              roomName: i['pubName'],
                                              roomPrivName: i['privName'],
                                              roomState: i['state'],
                                              switchState: (val) {
                                                commandRoom(val!, i['id']);
                                              },
                                              image: i['image']
                                                      .toString()
                                                      .contains(
                                                          'com.example.zaen')
                                                  ? FileImage(File(i['image']))
                                                  : AssetImage("${i['image']}"),
                                            )),
                                          const SizedBox(
                                            height: 30,
                                          ),
                                        ],
                                      ),
                                    ),
                                    SingleChildScrollView(
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal:
                                                controller.sizedWidth * 0.02),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            Text(
                                              'اختصارات سريعة',
                                              textAlign: TextAlign.justify,
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: AppColors
                                                      .backgroundColor2
                                                      .withOpacity(0.65),
                                                  fontSize:
                                                      controller.sized * 0.011,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              height:
                                                  controller.sizedHight * 0.005,
                                            ),
                                            routine(context),

                                            // Row(
                                            //   children: [
                                            //     Center(
                                            //       child: Container(
                                            //         height: controller.sizedHight*0.1,
                                            //         width: controller.sizedWidth*0.1,
                                            //         child: Chart(
                                            //           layers: [

                                            //             ChartGroupPieLayer(
                                            //               items: List.generate(
                                            //                 2,
                                            //                 (index) => List.generate(
                                            //                   3,
                                            //                   (index) => ChartGroupPieDataItem(
                                            //                       amount: 500,
                                            //                       color: [
                                            //                         Colors.amber,
                                            //                         Colors.pinkAccent,
                                            //                         Colors.redAccent,
                                            //                         Colors.blueAccent,
                                            //                         Colors.cyanAccent,
                                            //                         Colors.tealAccent,
                                            //                       ][3],
                                            //                       label: [
                                            //                         'Life',
                                            //                         'Work',
                                            //                         'Medicine',
                                            //                         'Bills',
                                            //                         'Hobby',
                                            //                         'Holiday',
                                            //                       ][3]),
                                            //                 ),
                                            //               ),
                                            //               settings: const ChartGroupPieSettings(
                                            //                 gapSweepAngle: 10,
                                            //                 thickness: 4,
                                            //                 gapBetweenChartCircles: 7

                                            //               ),
                                            //             ),
                                            //             ],
                                            //         ),
                                            //       )
                                            //     ),
                                            //   ],
                                            // ),

                                            Divider(
                                              color: AppColors.backgroundColor
                                                  .withOpacity(0.2),
                                              endIndent:
                                                  controller.sizedWidth * 0.1,
                                            ),
                                            Padding(
                                              padding: EdgeInsets.zero,
                                              child: Row(
                                                textDirection:
                                                    TextDirection.rtl,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      'الملحقات المفضلة',
                                                      textAlign:
                                                          TextAlign.justify,
                                                      textDirection:
                                                          TextDirection.rtl,
                                                      style: TextStyle(
                                                          color: AppColors
                                                              .backgroundColor2
                                                              .withOpacity(
                                                                  0.65),
                                                          fontSize:
                                                              controller.sized *
                                                                  0.011,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    ),
                                                  ),
                                                  editFavorite
                                                      ? CircularPercentIndicator(
                                                          radius:
                                                              controller.sized *
                                                                  0.0175,
                                                          lineWidth:
                                                              controller.sized *
                                                                  0.01,
                                                          backgroundWidth:
                                                              controller.sized *
                                                                  0.001,
                                                          backgroundColor:
                                                              AppColors
                                                                  .primaryColor
                                                                  .withOpacity(
                                                                      0.7),
                                                          center: IconButton(
                                                              padding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              color: AppColors
                                                                  .primaryColor
                                                                  .withOpacity(
                                                                      0.8),
                                                              iconSize:
                                                                  controller
                                                                          .sized *
                                                                      0.0225,
                                                              onPressed:
                                                                  () async {
                                                                print(controller
                                                                    .rooms);
                                                                ;
                                                                setState(() {
                                                                  editFavorite =
                                                                      false;
                                                                });
                                                                var appDB =
                                                                    await openDatabase(
                                                                        '${controller.system}.db',
                                                                        version:
                                                                            3);
                                                                await appDB
                                                                    .rawQuery(
                                                                        "DELETE FROM favorite");
                                                                for (var i
                                                                    in controller
                                                                        .favorite) {
                                                                  await appDB
                                                                      .rawQuery(
                                                                          'insert into favorite(id) values(?)',
                                                                          [i]);
                                                                }
                                                              },
                                                              icon: Icon(Icons
                                                                  .check_circle)),
                                                        )
                                                      : CircularPercentIndicator(
                                                          radius:
                                                              controller.sized *
                                                                  0.0175,
                                                          lineWidth:
                                                              controller.sized *
                                                                  0.01,
                                                          backgroundWidth:
                                                              controller.sized *
                                                                  0.001,
                                                          backgroundColor:
                                                              AppColors
                                                                  .backgroundColor
                                                                  .withOpacity(
                                                                      0.3),
                                                          center: IconButton(
                                                              padding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              color: AppColors
                                                                  .backgroundColor
                                                                  .withOpacity(
                                                                      0.5),
                                                              iconSize: controller
                                                                      .sized *
                                                                  0.0225,
                                                              onPressed: () {
                                                                setState(() {
                                                                  editFavorite =
                                                                      true;
                                                                });
                                                              },
                                                              icon: Icon(Icons
                                                                  .change_circle)),
                                                        ),
                                                  editFavorite
                                                      ? CircularPercentIndicator(
                                                          radius:
                                                              controller.sized *
                                                                  0.0175,
                                                          lineWidth:
                                                              controller.sized *
                                                                  0.01,
                                                          backgroundWidth:
                                                              controller.sized *
                                                                  0.001,
                                                          backgroundColor:
                                                              AppColors
                                                                  .errorColor
                                                                  .withOpacity(
                                                                      0.7),
                                                          center: IconButton(
                                                              padding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              color: AppColors
                                                                  .errorColor
                                                                  .withOpacity(
                                                                      0.8),
                                                              iconSize: controller
                                                                      .sized *
                                                                  0.0225,
                                                              onPressed: () {
                                                                setState(() {
                                                                  editFavorite =
                                                                      false;
                                                                });
                                                                final builder =
                                                                    MqttClientPayloadBuilder();

                                                                builder
                                                                    .addString(
                                                                        're');

                                                                builder
                                                                    .addString(
                                                                        '1');
                                                                client.publishMessage(
                                                                    'edit',
                                                                    MqttQos
                                                                        .atLeastOnce,
                                                                    builder
                                                                        .payload!);
                                                              },
                                                              icon: Icon(Icons
                                                                  .cancel)),
                                                        )
                                                      : Container()
                                                ],
                                              ),
                                            ),
                                            SizedBox(
                                              height:
                                                  controller.sizedHight * 0.005,
                                            ),
                                            editFavorite
                                                ? EditFavorite()
                                                : favorite(),
                                            Divider(
                                              color: AppColors.border
                                                  .withOpacity(0.4),
                                              endIndent:
                                                  controller.sizedWidth * 0.1,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ]),
                            );
                          },
                        ),
                      ),
                      if (menublur)
                        BackdropFilter(
                          blendMode: BlendMode.srcOver,
                          filter: ImageFilter.blur(
                            sigmaX: 10,
                            sigmaY: 10,
                          ),
                          child: Container(
                            height: MediaQuery.of(context).size.height,
                            width: MediaQuery.of(context).size.width,
                          ),
                        ),
                    ],
                  ),
                )));
  }

  // void switchTap(String Switch, bool state, String id) {
  //   // setState(() {
  //   controller.rooms[controller.rooms
  //       .indexWhere((element) => element['id'] == id)][Switch] = !state;
  //   // });
  // }
}

/////////////////////////////////////////////////////////
// الصوت + -
// القنوات + -
// صامت
// الارقام

// InternetAddress.lookup('zain.local.com').then((value) {
//   print(value);
// }).catchError((val) {
//   print(val);
// });

// gettingIP() async {
//   await Permission.location.request();
//   final info = NetworkInfo();
//   var hostAddress = await info.getWifiIP();
//   return hostAddress;
// }

// print(gettingIP().then((value) {
//   print(value);
// }));
