import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/controller/controller.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:wifi_scan/wifi_scan.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:http/http.dart' as http;

class wait extends StatefulWidget {
  const wait({Key? key}) : super(key: key);

  @override
  State<wait> createState() => _waitState();
}

class _waitState extends State<wait> {
  HomeController controller = Get.put(HomeController(), permanent: true);

  Timer? LoadingTimer;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    LoadingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    controller.sizedHight = MediaQuery.of(context).size.height;
    controller.sizedWidth = MediaQuery.of(context).size.width;
    controller.sized = controller.sizedHight + controller.sizedWidth;
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          leading: IconButton(
            icon: iconStyle(icon: Icons.account_circle_rounded),
            onPressed: () {
              TextEditingController editPriv = TextEditingController(
                text: controller.deviceName,
              );
              bool privN = false;
              AwesomeDialog(
                context: context,
                dialogType: DialogType.noHeader,
                padding: EdgeInsets.only(
                    bottom: controller.sized * 0.01,
                    top: controller.sized * 0.005),
                animType: AnimType.scale,
                dialogBorderRadius: BorderRadius.circular(25),
                dialogBackgroundColor: AppColors.backgroundColor2,
                body: GetBuilder<HomeController>(builder: (controller) {
                  return StatefulBuilder(
                    builder: (BuildContext context, StateSetter setState) {
                      print(controller.systems);
                      print(controller.deviceName + controller.deviceModel);

                      return Column(
                        children: [
                          txtStyle(
                              txt: 'المعلومات الشخصيه', align: TextAlign.right),
                          SizedBox(
                            height: controller.sizedHight * 0.02,
                          ),
                          Row(
                            children: [
                              if (privN)
                                IconButton(
                                    onPressed: () async {
                                      // حفظ الاسم الجديد في SharedPreferences
                                      final prefs =
                                          await SharedPreferences.getInstance();
                                      await prefs.setString(
                                          'device_name', editPriv.text);

                                      // تحديث الكنترولر
                                      controller.deviceName = editPriv.text;
                                      controller.update();

                                      // إخفاء زر التأكيد
                                      setState(() {
                                        privN = false;
                                      });

                                      // إظهار رسالة نجاح
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text('تم حفظ الاسم بنجاح'),
                                          backgroundColor:
                                              AppColors.primaryColor,
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                    },
                                    icon: Icon(
                                      Icons.done,
                                      color: AppColors.primaryColor,
                                    )),
                              Expanded(
                                child: Padding(
                                  padding:
                                      EdgeInsets.all(controller.sized * 0.01),
                                  child: Container(
                                    width: controller.sizedWidth * 0.85,
                                    child: TextFormField(
                                      controller: editPriv,
                                      maxLength: 15,
                                      scrollPadding: EdgeInsets.all(10),
                                      showCursor: true,
                                      cursorColor: AppColors.primary,
                                      textDirection: TextDirection.rtl,
                                      style: TextStyle(
                                        color: AppColors.textPrimary,
                                        fontSize: controller.sized * 0.015,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      onChanged: (i) {
                                        setState(() {
                                          privN = true;
                                        });
                                      },
                                      decoration: InputDecoration(
                                        hintText: 'الاسم',
                                        hintStyle: TextStyle(
                                          color: AppColors.textHint,
                                          fontSize: controller.sized * 0.014,
                                          fontWeight: FontWeight.normal,
                                        ),
                                        filled: true,
                                        fillColor: AppColors.surface,
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal:
                                              controller.sizedWidth * 0.04,
                                          vertical:
                                              controller.sizedHight * 0.015,
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          borderSide: BorderSide(
                                            color: AppColors.border,
                                            width: 1.0,
                                          ),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          borderSide: BorderSide(
                                            color: AppColors.border,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          borderSide: BorderSide(
                                            color: AppColors.primary,
                                            width: 2.0,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              txtStyle(
                                  txt: controller.deviceModel,
                                  color: AppColors.textPrimary,
                                  align: TextAlign.right),
                              txtStyle(
                                  txt: 'نوع الجهاز :       ',
                                  color: AppColors.textHint),
                            ],
                          ),
                          SizedBox(
                            height: controller.sizedHight * 0.02,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              txtStyle(
                                  align: TextAlign.right,
                                  color: AppColors.textColor.withOpacity(0.8),
                                  txt: controller.uuid
                                      .toString()
                                      .replaceAll('-', '-\n'),
                                  maxLines: 5),
                              txtStyle(
                                  txt: 'رمز الجهاز :       ',
                                  color: AppColors.textColor.withOpacity(0.3)),
                            ],
                          ),
                        ],
                      );
                    },
                  );
                }),
              ).show();
            },
          ),
        ),
        body: GetBuilder<HomeController>(
          builder: (controller) => Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: AppColors.backgroundGradient,
            ),
            child: Container(
              child: Builder(
                builder: (context) => SingleChildScrollView(
                    physics: NeverScrollableScrollPhysics(),
                    child: Container(
                      child: Column(
                        children: [
                          SizedBox(
                            height: controller.sizedHight * 0.07,
                          ),
                          Image.asset(
                            height: controller.sizedHight * 0.25,
                            width: controller.sizedWidth * 0.45,
                            'assets/images/4444.png',
                          ),
                          txtStyle(
                              txt: 'امنح الحياه و الذكاء\n الى مساحاتك',
                              maxLines: 2,
                              color: AppColors.textColor3,
                              size: controller.sized * 0.03),
                          SizedBox(
                            height: controller.sizedHight * 0.35,
                          ),
                          ElevatedButton(
                            style: ButtonStyle(
                              backgroundColor:
                                  WidgetStateProperty.resolveWith<Color?>(
                                (Set<WidgetState> states) {
                                  if (states.contains(WidgetState.pressed)) {
                                    return AppColors
                                        .textColor; // اللون عند الضغط على الزر
                                  }
                                  return AppColors
                                      .textColor3; // اللون الافتراضي
                                },
                              ),
                            ),
                            onPressed: () async {
                              AwesomeDialog(
                                context: context,
                                dialogType: DialogType.noHeader,
                                padding: EdgeInsets.only(
                                    bottom: controller.sized * 0.01,
                                    top: controller.sized * 0.005),
                                animType: AnimType.scale,
                                dialogBorderRadius: BorderRadius.circular(25),
                                dialogBackgroundColor: AppColors.surface,
                                body: GetBuilder<HomeController>(
                                    builder: (controller) {
                                  return StatefulBuilder(
                                    builder: (BuildContext context,
                                        StateSetter setState) {
                                      print(controller.systems);
                                      print(controller.deviceName +
                                          controller.deviceModel);

                                      return Container(
                                        constraints: BoxConstraints(
                                            maxHeight: controller
                                                        .systems.length ==
                                                    4
                                                ? controller.sizedHight * 0.325
                                                : controller.systems.length == 3
                                                    ? controller.sizedHight *
                                                        0.25
                                                    : controller.systems
                                                                .length ==
                                                            2
                                                        ? controller
                                                                .sizedHight *
                                                            0.175
                                                        : controller.systems
                                                                    .length ==
                                                                1
                                                            ? controller
                                                                    .sizedHight *
                                                                0.1
                                                            : controller
                                                                    .sizedHight *
                                                                0.4),
                                        child: Column(
                                          children: [
                                            txtStyle(
                                                txt: 'الانظمة الذكية المرتبطه',
                                                align: TextAlign.right),
                                            SizedBox(
                                              height:
                                                  controller.sizedHight * 0.02,
                                            ),
                                            Expanded(
                                              child: SingleChildScrollView(
                                                child: Column(
                                                  children: [
                                                    for (var system
                                                        in controller.systems)
                                                      Container(
                                                        margin: EdgeInsets.only(
                                                            bottom: controller
                                                                    .sizedHight *
                                                                0.01),
                                                        child: ElevatedButton(
                                                          onPressed: () async {
                                                            try {
                                                              // إغلاق الحوار الحالي أولاً
                                                              Navigator.pop(
                                                                  context);

                                                              // التحقق من حالة الاتصال والاتصال إذا لزم الأمر
                                                              if (controller
                                                                      .hostZain
                                                                      .isNotEmpty &&
                                                                  client
                                                                          .connectionStatus!
                                                                          .state
                                                                          .name !=
                                                                      'connected') {
                                                                debugPrint(
                                                                    'محاولة الاتصال بالخادم للنظام: ${system['title']}');
                                                                connect();

                                                                // انتظار قصير للاتصال
                                                                await Future.delayed(
                                                                    const Duration(
                                                                        seconds:
                                                                            2));

                                                                // التحقق من نجاح الاتصال
                                                                if (client
                                                                        .connectionStatus!
                                                                        .state
                                                                        .name !=
                                                                    'connected') {
                                                                  throw Exception(
                                                                      'فشل في الاتصال بالخادم');
                                                                }
                                                              }

                                                              // إرسال طلب البحث للنظام المحدد
                                                              if (client
                                                                      .connectionStatus!
                                                                      .state
                                                                      .name ==
                                                                  'connected') {
                                                                debugPrint(
                                                                    'إرسال طلب البحث للنظام: ${system['title']}');

                                                                client.subscribe(
                                                                    controller
                                                                        .uuid,
                                                                    MqttQos
                                                                        .atLeastOnce);
                                                                final builder =
                                                                    MqttClientPayloadBuilder();
                                                                builder.addString(
                                                                    controller
                                                                        .uuid);

                                                                client.publishMessage(
                                                                    'phone/ask',
                                                                    MqttQos
                                                                        .atLeastOnce,
                                                                    builder
                                                                        .payload!);
                                                                debugPrint(
                                                                    'تم إرسال طلب البحث للنظام: ${system['title']} بنجاح');
                                                              } else {
                                                                throw Exception(
                                                                    'لا يوجد اتصال بالخادم');
                                                              }

                                                              // إعداد التايمر وإظهار الحوار
                                                              bool loading =
                                                                  false;
                                                              if (LoadingTimer !=
                                                                  null) {
                                                                setState(() {
                                                                  LoadingTimer!
                                                                      .cancel();
                                                                });
                                                              }
                                                              LoadingTimer =
                                                                  null;

                                                              // التحقق من أن الويدجت ما زال موجود قبل إظهار الحوار
                                                              if (!mounted)
                                                                return;

                                                              // إظهار حوار البحث للنظام المحدد
                                                              AwesomeDialog(
                                                                context:
                                                                    context,
                                                                dialogType:
                                                                    DialogType
                                                                        .info,
                                                                animType:
                                                                    AnimType
                                                                        .scale,
                                                                dialogBackgroundColor:
                                                                    AppColors
                                                                        .backgroundColor2,
                                                                body:
                                                                    StatefulBuilder(
                                                                  builder: (BuildContext
                                                                          context,
                                                                      StateSetter
                                                                          setDialogState) {
                                                                    load() async {
                                                                      try {
                                                                        if (mounted &&
                                                                            !loading) {
                                                                          setDialogState(
                                                                              () {
                                                                            loading =
                                                                                true;
                                                                            if (LoadingTimer !=
                                                                                null) {
                                                                              LoadingTimer!.cancel();
                                                                            }
                                                                          });
                                                                        }
                                                                      } catch (e) {
                                                                        // معالجة أخطاء التحميل
                                                                        debugPrint(
                                                                            'خطأ في عملية التحميل للنظام ${system['title']}: $e');
                                                                        if (LoadingTimer !=
                                                                            null) {
                                                                          LoadingTimer!
                                                                              .cancel();
                                                                        }
                                                                        // إعادة تعيين حالة التحميل
                                                                        if (mounted) {
                                                                          setDialogState(
                                                                              () {
                                                                            loading =
                                                                                false;
                                                                          });
                                                                        }
                                                                      }

                                                                      debugPrint(
                                                                          'تم بدء عملية البحث عن النظام: ${system['title']}');
                                                                    }

                                                                    LoadingTimer = Timer(
                                                                        Duration(
                                                                            seconds:
                                                                                10),
                                                                        () {
                                                                      if (mounted) {
                                                                        setDialogState(
                                                                            () {
                                                                          loading =
                                                                              false;
                                                                        });
                                                                      }
                                                                    });

                                                                    return Container(
                                                                      height:
                                                                          controller.sizedHight *
                                                                              0.3,
                                                                      child:
                                                                          Column(
                                                                        children: [
                                                                          // عنوان النظام
                                                                          txtStyle(
                                                                            txt:
                                                                                'البحث عن النظام: ${system['title']}',
                                                                            align:
                                                                                TextAlign.center,
                                                                            size:
                                                                                controller.sized * 0.016,
                                                                            color:
                                                                                AppColors.textColor,
                                                                          ),

                                                                          SizedBox(
                                                                              height: controller.sizedHight * 0.03),

                                                                          // حالة التحميل أو النتيجة
                                                                          if (loading) ...[
                                                                            CircularProgressIndicator(
                                                                              color: AppColors.primaryColor,
                                                                            ),
                                                                            SizedBox(height: controller.sizedHight * 0.02),
                                                                            txtStyle(
                                                                              txt: 'جاري البحث عن النظام...',
                                                                              align: TextAlign.center,
                                                                              color: AppColors.textColor.withOpacity(0.7),
                                                                            ),
                                                                          ] else ...[
                                                                            Icon(
                                                                              Icons.wifi_off,
                                                                              size: controller.sized * 0.05,
                                                                              color: AppColors.warningColor,
                                                                            ),
                                                                            SizedBox(height: controller.sizedHight * 0.02),
                                                                            txtStyle(
                                                                              txt: 'لم يتم العثور على النظام',
                                                                              align: TextAlign.center,
                                                                              color: AppColors.warningColor,
                                                                            ),
                                                                            SizedBox(height: controller.sizedHight * 0.01),
                                                                            txtStyle(
                                                                              txt: 'تأكد من أن النظام متصل بالشبكة',
                                                                              align: TextAlign.center,
                                                                              color: AppColors.textColor.withOpacity(0.6),
                                                                              size: controller.sized * 0.012,
                                                                            ),
                                                                          ],

                                                                          Spacer(),

                                                                          // زر إعادة المحاولة أو الإغلاق
                                                                          ElevatedButton(
                                                                            style:
                                                                                ButtonStyle(
                                                                              backgroundColor: WidgetStateProperty.resolveWith<Color?>(
                                                                                (Set<WidgetState> states) {
                                                                                  if (states.contains(WidgetState.pressed)) {
                                                                                    return AppColors.primaryColor;
                                                                                  }
                                                                                  return loading ? AppColors.warningColor : AppColors.primaryColor;
                                                                                },
                                                                              ),
                                                                            ),
                                                                            onPressed: loading
                                                                                ? null
                                                                                : () {
                                                                                    if (loading) return;

                                                                                    // إعادة المحاولة
                                                                                    setDialogState(() {
                                                                                      loading = true;
                                                                                    });
                                                                                    load();
                                                                                  },
                                                                            child:
                                                                                txtStyle(
                                                                              txt: loading ? 'جاري البحث...' : 'إعادة المحاولة',
                                                                              color: AppColors.white,
                                                                              size: controller.sized * 0.014,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    );
                                                                  },
                                                                ),
                                                              ).show();
                                                            } on SocketException catch (e) {
                                                              // معالجة أخطاء الشبكة
                                                              debugPrint(
                                                                  'خطأ في الاتصال بالشبكة للنظام ${system['title']}: $e');
                                                              client
                                                                  .disconnect();
                                                              await Future.delayed(
                                                                  const Duration(
                                                                      seconds:
                                                                          5));

                                                              // إظهار رسالة خطأ للمستخدم
                                                              if (mounted) {
                                                                ScaffoldMessenger.of(
                                                                        context)
                                                                    .showSnackBar(
                                                                  SnackBar(
                                                                    content: Text(
                                                                        'خطأ في الاتصال بالشبكة للنظام: ${system['title']}'),
                                                                    backgroundColor:
                                                                        AppColors
                                                                            .errorColor,
                                                                    duration: const Duration(
                                                                        seconds:
                                                                            3),
                                                                  ),
                                                                );
                                                              }
                                                            } on TimeoutException catch (e) {
                                                              // معالجة انتهاء المهلة الزمنية
                                                              debugPrint(
                                                                  'انتهت المهلة الزمنية للنظام ${system['title']}: $e');
                                                              if (mounted) {
                                                                ScaffoldMessenger.of(
                                                                        context)
                                                                    .showSnackBar(
                                                                  SnackBar(
                                                                    content: Text(
                                                                        'انتهت المهلة الزمنية للاتصال بالنظام: ${system['title']}'),
                                                                    backgroundColor:
                                                                        AppColors
                                                                            .warningColor,
                                                                    duration: const Duration(
                                                                        seconds:
                                                                            3),
                                                                  ),
                                                                );
                                                              }
                                                            } catch (e) {
                                                              // معالجة الأخطاء العامة
                                                              debugPrint(
                                                                  'خطأ غير متوقع للنظام ${system['title']}: $e');
                                                              if (mounted) {
                                                                ScaffoldMessenger.of(
                                                                        context)
                                                                    .showSnackBar(
                                                                  SnackBar(
                                                                    content: Text(
                                                                        'حدث خطأ غير متوقع مع النظام: ${system['title']}'),
                                                                    backgroundColor:
                                                                        AppColors
                                                                            .errorColor,
                                                                    duration: const Duration(
                                                                        seconds:
                                                                            3),
                                                                  ),
                                                                );
                                                              }
                                                            }
                                                          },
                                                          style: ButtonStyle(
                                                            backgroundColor:
                                                                WidgetStateProperty
                                                                    .resolveWith<
                                                                        Color?>(
                                                              (Set<WidgetState>
                                                                  states) {
                                                                if (states.contains(
                                                                    WidgetState
                                                                        .pressed)) {
                                                                  return AppColors
                                                                      .textColor
                                                                      .withOpacity(
                                                                          0.5); // اللون عند الضغط على الزر
                                                                }
                                                                return AppColors
                                                                    .backgroundColor3
                                                                    .withOpacity(
                                                                        0.4); // اللون الافتراضي
                                                              },
                                                            ),
                                                            elevation:
                                                                WidgetStateProperty
                                                                    .resolveWith<
                                                                        double>(
                                                              (Set<WidgetState>
                                                                  states) {
                                                                return 0; // الحالة العادية
                                                              },
                                                            ),
                                                            padding:
                                                                WidgetStateProperty
                                                                    .all(EdgeInsets
                                                                        .zero),

                                                            // إضافة radius للزر
                                                            shape:
                                                                WidgetStateProperty
                                                                    .all(
                                                              RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12), // يمكنك تغيير الرقم حسب الحاجة
                                                              ),
                                                            ),

                                                            // تحديد الحجم الأدنى للزر
                                                            minimumSize:
                                                                WidgetStateProperty
                                                                    .all(
                                                              Size(
                                                                  controller
                                                                          .sizedWidth *
                                                                      0.72,
                                                                  controller
                                                                          .sizedHight *
                                                                      0.06), // يمكنك تعديل القيم
                                                            ),
                                                            maximumSize:
                                                                WidgetStateProperty
                                                                    .all(
                                                              Size(
                                                                  controller
                                                                          .sizedWidth *
                                                                      0.72,
                                                                  controller
                                                                          .sizedHight *
                                                                      0.06), // يمكنك تعديل القيم
                                                            ),
                                                          ),
                                                          child: Container(
                                                            margin: EdgeInsets
                                                                .all(controller
                                                                        .sized *
                                                                    0.01),
                                                            child: Row(
                                                              textDirection:
                                                                  TextDirection
                                                                      .rtl,
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                txtStyle(
                                                                    txt: system[
                                                                        'title']),
                                                                txtStyle(
                                                                    txt: system['type'] ==
                                                                            'full'
                                                                        ? 'وصول كامل'
                                                                        : system['type'] ==
                                                                                'never'
                                                                            ? 'عدم الوصول'
                                                                            : 'وصول جزئي',
                                                                    color: system['type'] ==
                                                                            'full'
                                                                        ? AppColors
                                                                            .primaryColor
                                                                        : system['type'] ==
                                                                                'never'
                                                                            ? AppColors.warningColor
                                                                            : AppColors.primaryColor)
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                }),
                              ).show();
                            },
                            // padding: EdgeInsets.all(15),
                            // color: AppColors.textColor3,
                            child: txtStyle(
                                txt: 'قائمه الانظمه المرتبطه',
                                color: AppColors.backgroundColor2,
                                size: controller.sized * 0.02),
                          ),
                          SizedBox(
                            height: controller.sizedHight * 0.02,
                          ),
                          if (controller.wifi.isNotEmpty)
                            ElevatedButton(
                              // shape: RoundedRectangleBorder(
                              //   borderRadius:
                              //       BorderRadius.circular(15),
                              // ),
                              style: ButtonStyle(
                                backgroundColor:
                                    WidgetStateProperty.resolveWith<Color?>(
                                  (Set<WidgetState> states) {
                                    if (states.contains(WidgetState.pressed)) {
                                      return AppColors
                                          .primaryColor; // اللون عند الضغط على الزر
                                    }
                                    return AppColors
                                        .warningColor; // اللون الافتراضي
                                  },
                                ),
                              ),
                              onPressed: () async {
                                try {
                                  // التحقق من حالة الاتصال والاتصال إذا لزم الأمر
                                  if (controller.hostZain.isNotEmpty &&
                                      client.connectionStatus!.state.name !=
                                          'connected') {
                                    debugPrint('محاولة الاتصال بالخادم...');
                                    connect();

                                    // انتظار قصير للاتصال
                                    await Future.delayed(
                                        const Duration(seconds: 2));

                                    // التحقق من نجاح الاتصال
                                    if (client.connectionStatus!.state.name !=
                                        'connected') {
                                      throw Exception('فشل في الاتصال بالخادم');
                                    }
                                  }

                                  // إرسال طلب البحث عن النظام
                                  if (client.connectionStatus!.state.name ==
                                      'connected') {
                                    debugPrint('إرسال طلب البحث عن النظام...');

                                    client.subscribe(
                                        controller.uuid, MqttQos.atLeastOnce);
                                    final builder = MqttClientPayloadBuilder();
                                    builder.addString(controller.uuid);

                                    client.publishMessage('phone/ask',
                                        MqttQos.atLeastOnce, builder.payload!);
                                    debugPrint('تم إرسال طلب البحث بنجاح');
                                  } else {
                                    throw Exception('لا يوجد اتصال بالخادم');
                                  }
                                  bool loading = false;
                                  if (LoadingTimer != null) {
                                    setState(() {
                                      LoadingTimer!.cancel();
                                    });
                                  }
                                  LoadingTimer = null;

                                  // التحقق من أن الويدجت ما زال موجود قبل إظهار الحوار
                                  if (!mounted) return;

                                  AwesomeDialog(
                                    context: context,
                                    dialogType: DialogType.info,
                                    animType: AnimType.scale,
                                    dialogBackgroundColor:
                                        AppColors.backgroundColor2,
                                    body: GetBuilder<HomeController>(
                                        builder: (controller) {
                                      return StatefulBuilder(
                                        builder: (BuildContext context,
                                            StateSetter setState) {
                                          load() async {
                                            try {
                                              if (mounted && !loading) {
                                                setState(() {
                                                  loading = true;
                                                  if (LoadingTimer != null) {
                                                    LoadingTimer!.cancel();
                                                  }
                                                });
                                              }
                                            } catch (e) {
                                              // معالجة أخطاء التحميل
                                              debugPrint(
                                                  'خطأ في عملية التحميل: $e');
                                              if (LoadingTimer != null) {
                                                LoadingTimer!.cancel();
                                              }
                                              // إعادة تعيين حالة التحميل
                                              if (mounted) {
                                                setState(() {
                                                  loading = false;
                                                });
                                              }
                                            }

                                            debugPrint(
                                                'تم بدء عملية البحث عن النظام');
                                          }

                                          if (LoadingTimer == null &&
                                              loading == false) {
                                            LoadingTimer = Timer.periodic(
                                                Duration(seconds: 5),
                                                (timer) async {
                                              load();
                                            });
                                          }

                                          return ConditionalBuilder(
                                            condition:
                                                controller.sysMAC.isNotEmpty &&
                                                    client.connectionStatus!
                                                            .state.name ==
                                                        'connected',
                                            fallback: (context) {
                                              return !loading
                                                  ? Column(
                                                      children: [
                                                        SizedBox(
                                                          height: controller
                                                                  .sizedHight *
                                                              0.02,
                                                        ),
                                                        txtStyle(
                                                            txt:
                                                                'جاري البحث عن نظام',
                                                            color: AppColors
                                                                .textColor2),
                                                        SizedBox(
                                                          height: controller
                                                                  .sizedHight *
                                                              0.05,
                                                        ),
                                                        CircularProgressIndicator(),
                                                        SizedBox(
                                                          height: controller
                                                                  .sizedHight *
                                                              0.1,
                                                        ),
                                                      ],
                                                    )
                                                  : Column(
                                                      children: [
                                                        SizedBox(
                                                          height: controller
                                                                  .sizedHight *
                                                              0.02,
                                                        ),
                                                        Center(
                                                          child: txtStyle(
                                                              txt:
                                                                  'لم يتم العثور على نظام في شبكه WIFI',
                                                              color: AppColors
                                                                  .errorColor),
                                                        ),
                                                        SizedBox(
                                                          height: controller
                                                                  .sizedHight *
                                                              0.04,
                                                        ),
                                                      ],
                                                    );
                                            },
                                            builder: (context) {
                                              return Column(
                                                children: [
                                                  SizedBox(
                                                    height:
                                                        controller.sizedHight *
                                                            0.02,
                                                  ),
                                                  controller.sysMAC == "false"
                                                      ? txtStyle(
                                                          txt:
                                                              "تم العثور على نظام",
                                                          color: AppColors
                                                              .secondaryColor)
                                                      : txtStyle(
                                                          txt:
                                                              "انت مرتبط بالفعل في النظام!",
                                                          color: AppColors
                                                              .secondaryColor),
                                                  SizedBox(
                                                    height:
                                                        controller.sizedHight *
                                                            0.02,
                                                  ),
                                                  ElevatedButton(
                                                    style: ButtonStyle(
                                                      backgroundColor:
                                                          WidgetStateProperty
                                                              .resolveWith<
                                                                  Color?>(
                                                        (Set<WidgetState>
                                                            states) {
                                                          if (states.contains(
                                                              WidgetState
                                                                  .pressed)) {
                                                            return AppColors
                                                                .primaryColor; // اللون عند الضغط على الزر
                                                          }
                                                          return AppColors
                                                              .secondaryColor; // اللون الافتراضي
                                                        },
                                                      ),
                                                    ),
                                                    onPressed: () {
                                                      final builder =
                                                          MqttClientPayloadBuilder();

                                                      builder.addString(
                                                          controller.uuid);

                                                      client.publishMessage(
                                                          'phone/join',
                                                          MqttQos.atLeastOnce,
                                                          builder.payload!);

                                                      // يتم فتح مربع وانتظار عوده الرد اذا كان هناك نظام ام لا
                                                      // وقت الانتظار هو 10 ثواني
                                                      // اذا كان يوجد نظام يتم اظهار زر يوجد نظام
                                                      // يتم فحص ان كان النظام موجود في قاعده بيانات الهاتف
                                                      // اذا غير موجود وعند الضغط على الزر يتم ارسال طلب انضمام
                                                      // اذا لم يتم الرد, تظهر راساله بعدم وجود نظام
                                                    },
                                                    child: txtStyle(
                                                        txt: controller
                                                                    .sysMAC ==
                                                                "false"
                                                            ? 'ارسال طلب انضمام'
                                                            : 'الدخول الى النظام',
                                                        color: AppColors
                                                            .textColor3,
                                                        size: controller.sized *
                                                            0.015),
                                                  ),
                                                  SizedBox(
                                                    height:
                                                        controller.sizedHight *
                                                            0.03,
                                                  ),
                                                ],
                                              );
                                            },
                                          );
                                        },
                                      );
                                    }),
                                  ).show();
                                } on SocketException catch (e) {
                                  // معالجة أخطاء الشبكة
                                  debugPrint('خطأ في الاتصال بالشبكة: $e');
                                  client.disconnect();
                                  await Future.delayed(Duration(seconds: 5));

                                  // إظهار رسالة خطأ للمستخدم
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('خطأ في الاتصال بالشبكة'),
                                        backgroundColor: AppColors.errorColor,
                                        duration: Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                } on TimeoutException catch (e) {
                                  // معالجة انتهاء المهلة الزمنية
                                  debugPrint('انتهت المهلة الزمنية: $e');
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                            'انتهت المهلة الزمنية للاتصال'),
                                        backgroundColor: AppColors.warningColor,
                                        duration: Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  // معالجة الأخطاء العامة
                                  debugPrint('خطأ غير متوقع: $e');
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('حدث خطأ غير متوقع'),
                                        backgroundColor: AppColors.errorColor,
                                        duration: Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                }
                              },

                              child: txtStyle(
                                  txt: 'العثور على نظام جديد',
                                  color: AppColors.white,
                                  size: controller.sized * 0.02),
                            )
                        ],
                      ),
                    )),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Show snackbar.
