import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zaen/shared/themes/app_colors.dart';

class SettingsController extends GetxController {
  // متغير لحفظ حالة الوضع الداكن/الفاتح
  RxBool isDarkMode = false.obs;

  // مفتاح حفظ الإعدادات
  static const String _darkModeKey = 'dark_mode';

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  // تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      isDarkMode.value = prefs.getBool(_darkModeKey) ?? false;
    } catch (e) {
      print('خطأ في تحميل الإعدادات: $e');
    }
  }

  // تبديل الوضع الداكن/الفاتح
  Future<void> toggleTheme() async {
    try {
      isDarkMode.value = !isDarkMode.value;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_darkModeKey, isDarkMode.value);

      // تحديث الثيم في التطبيق
      Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);

      // تحديث جميع GetBuilder widgets
      update();
    } catch (e) {
      print('خطأ في حفظ الإعدادات: $e');
    }
  }

  // تعيين الوضع الداكن
  Future<void> setDarkMode(bool value) async {
    try {
      isDarkMode.value = value;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_darkModeKey, value);

      // تحديث الثيم في التطبيق
      Get.changeThemeMode(value ? ThemeMode.dark : ThemeMode.light);

      // تحديث جميع GetBuilder widgets
      update();
    } catch (e) {
      print('خطأ في حفظ الإعدادات: $e');
    }
  }

  // الحصول على الوضع الحالي
  bool get currentThemeMode => isDarkMode.value;

  // الحصول على نص الوضع الحالي
  String get currentThemeName =>
      isDarkMode.value ? 'الوضع الداكن' : 'الوضع الفاتح';
}
