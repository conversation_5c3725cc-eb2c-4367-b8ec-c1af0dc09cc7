// import 'dart:async';
// import 'dart:math';
// import 'package:mqtt_client/mqtt_client.dart';
// import 'package:mqtt_client/mqtt_server_client.dart';
// import 'package:mysql1/mysql1.dart';
// import 'package:zaen/modules/local/ip.dart';
// import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/themes/app_colors.dart';

// const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
// Random _rnd = Random();

// String getRandomString(int length) => String.fromCharCodes(Iterable.generate(
//     length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

// MqttServerClient client = MqttServerClient.withPort(
//     controller.hostZain.value, getRandomString(10), 1883);

// var conn;
// late Results getData;

// Future<MqttServerClient> connect() async {
//   client.logging(on: true);
//   client.setProtocolV311();

//   client.keepAlivePeriod = 3;
//   client.disconnectOnNoResponsePeriod = 3;

//   client.autoReconnect = true;
//   client.resubscribeOnAutoReconnect = true;

//   client.onAutoReconnect = onAutoReconnect;

//   client.onAutoReconnected = onAutoReconnected;

//   client.onConnected = onConnected;
//   client.onDisconnected = onDisconnected;
//   // client.onUnsubscribed = onUnsubscribed;
//   client.onSubscribed = onSubscribed;
//   client.onSubscribeFail = onSubscribeFail;
//   client.pongCallback = pong;

//   final connMessage = MqttConnectMessage()
//       // .authenticateAs('username', 'password')
//       .keepAliveFor(60)
//       .withWillTopic('willtopic')
//       .withWillMessage('Will message')
//       .startClean()
//       .withWillQos(MqttQos.atLeastOnce);
//   client.connectionMessage = connMessage;
//   try {
//     await client.connect();
//   } catch (e) {
//     // print('Exception: $e');
//     client.disconnect();
//   }

//   client.updates?.listen(onUpdate);

//   return client;
// }

// // connection succeeded

// void onUpdate(List<MqttReceivedMessage<MqttMessage>> c) {
//   final message = c[0].payload as MqttPublishMessage;
//   final payload =
//       MqttPublishPayload.bytesToStringAsString(message.payload.message);

//   // print('Received message:$payload from topic: ${c[0].topic}>');

//   if (c[0].topic == 'edit') {
//     // print('7777777777');
//     getDevices();
//   } else {
//     List m = payload.split(' ');
//     String roomId = m[0];
//     String type = m[1];
//     String id = m[2];
//     List massege = m.sublist(3);
//     // print(controller.rooms);
//     if (type == 'AC') {
//       controller.rooms[roomId]['devices'][id]['degree'] =
//           massege[1] == 'X' ? 16 : int.parse(massege[1]);

//       controller.rooms[roomId]['devices'][id]['speed'] = int.parse(massege[3]);

//       controller.rooms[roomId]['devices'][id]['type'] = massege[4] == 'AC'
//           ? 'تبريد'
//           : massege[4] == 'HEAT'
//               ? 'تدفئه'
//               : 'مروحه';

//       controller.rooms[roomId]['devices'][id]['state'] =
//           massege[0] == 'OFF' ? false : true;
//     } else if (type == 'TV') {
//       controller.rooms[roomId]['devices'][id]['state'] =
//           massege[0] == 'POWER-ON' ? true : false;

//       controller.rooms[roomId]['devices'][id]['sil'] =
//           massege[1] == 'SIL-ON' ? false : true;
//     } else if (type == 'SWITCH') {
//       controller.rooms[roomId]['devices'][id]['state'] = false;
//       for (var j in controller.rooms[roomId]['devices'][id].keys.toList()) {
//         if (j != 'id' &&
//             j != 'device' &&
//             j != 'state' &&
//             j != 'pub' &&
//             j != 'priv' &&
//             j != 'pubName' &&
//             j != 'privName') {
//           controller.rooms[roomId]['devices'][id][j]['state'] = massege[massege
//                           .indexWhere((element) => element.split('_')[0] == j)]
//                       .split('_')[1] ==
//                   'OFF'
//               ? false
//               : true;
//           if (massege[massege
//                       .indexWhere((element) => element.split('_')[0] == j)]
//                   .split('_')[1] ==
//               'RUN') {
//             controller.rooms[roomId]['devices'][id]['state'] = true;
//           }
//         }
//       }
//     } else if (type.contains('ZAIN')) {}

//     var roomState = false;
//     for (var j in controller.rooms[roomId]['devices'].values) {
//       if (j['state'] == true) {
//         roomState = true;
//       }
//       // setState(() {

//       // });
//     }
//     controller.rooms[roomId]['state'] = roomState;
//     if (roomState == true) {
//       controller.homeState = true;
//     } else {
//       controller.homeState = false;
//       for (var i in controller.rooms.values) {
//         if (i['state'] == true) {
//           controller.homeState = true;
//         }
//       }
//     }
//     controller.update();
//   }
// }

// void onConnected() async {
//   // print('Connected');
//   try {
//     conn = await MySqlConnection.connect(ConnectionSettings(
//         host: controller.hostZain.value,
//         // port: 80,
//         user: 'root',
//         db: 'zain',
//         password: 'zain',
//         characterSet: CharacterSet.UTF8));
//   } catch (e) {}
// }

// // unconnected
// void onDisconnected() {
//   // print('Disconnected 111111111111111111111111111111111111111111112223');
//   if (client.connectionStatus!.state.name != 'connected') {
//     controller.update();
//   }
// }

// // subscribe to topic succeeded
// void onSubscribed(String topic) {
//   // print('Subscribed topic: $topic');
// }

// // subscribe to topic failed
// void onSubscribeFail(String topic) {
//   // print('Failed to subscribe $topic');
// }

// // unsubscribe succeeded
// void onUnsubscribed(String topic) {
//   // print('Unsubscribed topic: $topic');
// }

// // PING response received

// void onAutoReconnect() {
//   // print(
//   // 'EXAMPLE::onAutoReconnect client callback - Client auto reconnection sequence will start');

//   controller.update();
// }

// /// The post auto re connect callback
// void onAutoReconnected() async {
//   await findIp();

//   await getDevices();
//   // print(
//   // 'EXAMPLE::onAutoReconnected client callback - Client auto reconnection sequence has completed');
//   // controller.onInit();

//   conn = await MySqlConnection.connect(ConnectionSettings(
//       host: controller.hostZain.value,
//       // port: 80,
//       user: 'root',
//       db: 'zain',
//       password: 'zain',
//       characterSet: CharacterSet.UTF8));

//   controller.update();
// }

// Future<void> pong() async {
//   // print('Ping response client callback invoked');
//   // Devices = await conn.query('select * from Devices');
//   // // print(Devices.toList());
//   try {
//     getData = await conn.query("SELECT id,type,connect FROM Devices");
//     // print(getData);

//     List date;
//     for (var i in getData) {
//       if (i.fields['connect'] != null &&
//           i.fields['connect'].contains('-') &&
//           i.fields['type'].contains('ZAIN') == false) {
//         date = i.fields['connect'].split('-');
//         if (date.length == 5) {
//           date.add('0');
//         }
//         // print((DateTime.now().minute + (DateTime.now().second / 100)) -
//         //     (int.parse(date[4]) + (int.parse(date[5]) / 100)));
//         if (int.parse(date[0]) == DateTime.now().year &&
//             int.parse(date[1]) == DateTime.now().month &&
//             int.parse(date[2]) == DateTime.now().day &&
//             (int.parse(date[3]) == DateTime.now().hour ||
//                 (DateTime.now().hour - int.parse(date[3]) == 1 &&
//                     DateTime.now().minute == 0 &&
//                     int.parse(date[4]) == 59)) &&
//             ((DateTime.now().minute + (DateTime.now().second / 100)) -
//                     (int.parse(date[4]) + (int.parse(date[5]) / 100))) <
//                 0.3) {
//           if (controller.devices[i.fields['id']] == false) {
//             controller.devices[i.fields['id']] = true;
//             getDevices();
//           }
//         } else {
//           if (controller.devices[i.fields['id']] == true) {
//             controller.devices[i.fields['id']] = false;
//             getDevices();
//           }
//         }
//       }
//     }
//     var homeConnect = false;
//     for (var i in controller.rooms.values) {
//       var room = false;
//       // print(i['devices']);
//       // print('4444444444444444444422222222222');
//       for (var j in i['devices']) {
//         if (controller.devices[j['id']] == true) {
//           room = true;
//           homeConnect = true;
//         }
//       }
//       controller.devices[i['id']] = room;
//     }
//     controller.devices['home'] = homeConnect;

//     // print(controller.devices);
//   } catch (e) {}
// }
