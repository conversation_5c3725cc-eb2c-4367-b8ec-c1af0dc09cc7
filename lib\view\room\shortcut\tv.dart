import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:zaen/shared/commands/tv.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/models/shortcuts.dart';
import 'package:zaen/view/room/double_tap/del.dart';
import 'package:zaen/view/room/double_tap/tv_double_tap.dart';
import 'package:zaen/shared/themes/app_colors.dart';

roomTv({
  required var context,
  required var i,
}) =>
    shortcutTv(
        sizedWidth: controller.sizedWidth,
        sizedHeight: controller.sizedHight,
        sized: controller.sized,

        // image: AssetImage("assets/images/tv.jpg"),
        connect: controller.devices[i['id']],
        deviceState: i['state'],
        doubleTap: () {
          tvDoubleTap(context: context, i: i);
        },
        switchState: (val) {
          commandTvSw(val!, i, roomId);
        },
        tapOn_Tv_Icon: () {
          print('يعرض حاله التلفاز');
        },
        tapOn_VolumeUp: () {
          commandTvRemote('VOICE + 1', i, roomId);
        },
        tapOn_VolumeDown: () {
          commandTvRemote('VOICE - 1', i, roomId);
        },
        tapOn_ChUp: () {
          commandTvRemote('CH + 1', i, roomId);
        },
        tapOn_ChDown: () {
          commandTvRemote('CH - 1', i, roomId);
        },
        tapOn_VolumeMute: () {
          commandTvRemote(
              controller.rooms[roomId]['devices'][i['id']]['sil'] == true
                  ? 'SIL-OFF'
                  : 'SIL-ON',
              i,
              roomId);
        },
        tapOn_123: () {
          if (client.connectionStatus!.state.name == 'connected') {
            TextEditingController ch = TextEditingController();

            // _handleFABPressed();

            showBottomSheet(
                enableDrag: true,
                backgroundColor: Colors.transparent,
                context: context,
                builder: (context) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 25),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor.withOpacity(0.975),
                      borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(25.5)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FractionallySizedBox(
                          widthFactor: 0.25,
                          child: Container(
                            margin: const EdgeInsets.symmetric(
                              vertical: 16.0,
                            ),
                            child: Container(
                              height: 5.0,
                              decoration: BoxDecoration(
                                color: AppColors.textColor.withOpacity(0.5),
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(2.5)),
                              ),
                            ),
                          ),
                        ),
                        TextField(
                          autofocus: true,
                          controller: ch,
                          cursorColor: AppColors.primary,
                          style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: 25,
                              fontWeight: FontWeight.w600),
                          textInputAction: TextInputAction.done,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            hintText: "ادخل الرقم",
                            hintStyle: TextStyle(
                              color: AppColors.textHint,
                              fontSize: 20,
                              fontWeight: FontWeight.normal,
                            ),
                            filled: true,
                            fillColor: AppColors.surface,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 15,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.border,
                                width: 1.0,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.border,
                                width: 1.0,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.primary,
                                width: 2.0,
                              ),
                            ),
                          ),
                          keyboardType: TextInputType.number,
                          maxLength: 10,
                        ),
                        CircleAvatar(
                            radius: 20,
                            backgroundColor: AppColors.primaryColor,
                            child: IconButton(
                              padding: const EdgeInsets.only(left: 10),
                              onPressed: () {
                                if (ch.text.isNumericOnly) {
                                  // if (controller.rooms[roomId]['devices']
                                  //         [i['id']]['state'] ==
                                  //     false) {
                                  //   switchTap('state', i['state'], i['id']);
                                  // }
                                  // controller.rooms[roomId]['state'] = true;
                                  // controller.homeState = true;
                                  commandTvRemote('CH = ' + ch.text, i, roomId);
                                }
                              },
                              icon: Icon(
                                Icons.arrow_back_ios,
                                size: 30,
                                color:
                                    AppColors.backgroundColor.withOpacity(0.85),
                              ),
                            )),
                        const SizedBox(
                          height: 25,
                        )
                      ],
                    ),
                  );
                });
            print(ch);

            controller.update();
          }
        },
        tapOn_menu: () {
          if (client.connectionStatus!.state.name == 'connected') {
            if (controller.rooms[roomId]['devices'][i['id']]['state'] == false)
              switchTap('state', i['state'], i['id']);
            controller.rooms[roomId]['state'] = true;
            controller.homeState = true;

            final builder = MqttClientPayloadBuilder();

            builder.addString(i['id'] + ' TV M');
            client.publishMessage(controller.homeId + "/app/zain",
                MqttQos.atLeastOnce, builder.payload!);

            controller.update();
          }
        },
        tapOn_star: () {
          if (client.connectionStatus!.state.name == 'connected') {
            Map ch = controller.rooms[roomId]['devices'][i['id']]['ch'];
            var selectitem = 0;

            showBottomSheet(
                enableDrag: true,
                backgroundColor: Colors.transparent,
                context: context,
                builder: (context) {
                  return Container(
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor.withOpacity(0.975),
                      borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(25.5)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FractionallySizedBox(
                          widthFactor: 0.25,
                          child: Container(
                            margin: const EdgeInsets.symmetric(
                              vertical: 16.0,
                            ),
                            child: Container(
                              height: 5.0,
                              decoration: BoxDecoration(
                                color: AppColors.textColor.withOpacity(0.5),
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(2.5)),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 200,
                          child: CupertinoPicker(
                            squeeze: 0.9,
                            // scrollController:
                            //     FixedExtentScrollController(
                            //         initialItem:
                            //             3),
                            itemExtent: 40, //height of each item

                            looping: false,

                            magnification: 1.2,
                            backgroundColor: Colors.transparent,
                            children: <Widget>[
                              for (var c in ch.keys)
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.2),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Expanded(
                                        child: Text(ch[c],
                                            style: TextStyle(
                                              color: AppColors.textColor
                                                  .withOpacity(0.85),
                                              fontSize:
                                                  controller.sized * 0.016,
                                              fontWeight: FontWeight.bold,
                                            )),
                                      ),
                                      Text(
                                        c,
                                        style: TextStyle(
                                          color: AppColors.textColor
                                              .withOpacity(0.85),
                                          fontSize: controller.sized * 0.016,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                            onSelectedItemChanged: (int index) {
                              selectitem = index;
                            },
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        CircleAvatar(
                            radius: 20,
                            backgroundColor: AppColors.primaryColor,
                            child: IconButton(
                              padding: const EdgeInsets.only(left: 10),
                              onPressed: () {
                                // if (controller.rooms[roomId]['devices']
                                //         [i['id']]['state'] ==
                                //     false) {
                                //   switchTap('state', i['state'], i['id']);
                                // }
                                // controller.rooms[roomId]['state'] = true;
                                // controller.homeState = true;

                                commandTvRemote(
                                    'CH = ' + ch[ch.keys.toList()[selectitem]],
                                    i,
                                    roomId);
                              },
                              icon: Icon(
                                Icons.arrow_back_ios,
                                size: 30,
                                color:
                                    AppColors.backgroundColor.withOpacity(0.85),
                              ),
                            )),
                        const SizedBox(
                          height: 25,
                        )
                      ],
                    ),
                  );
                });

            controller.homeState = true;

            controller.update();
          }
        },
        tvPrivName: i['priv'],
        sil: i['sil']);
