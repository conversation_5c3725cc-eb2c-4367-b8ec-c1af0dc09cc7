import 'dart:io';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'dart:math' as math;
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/themes/app_dimensions.dart';
import 'package:zaen/shared/themes/app_typography.dart';
import 'package:zaen/view/room/double_tap/del.dart';

int? pageHor;
int? pageVer;
int? _activePage;

Widget RoomPage({
  String? roomPrivName,
  var image,
  String? homeType,
  String place = 'الغرفة',
  Function()? addRoom,
  Function()? nunDevices,
  required Function() del,
  required Function() asset,
  required Function() roll,
  required Function() camera,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: roomPrivName != 'x' ? roomPrivName : 'X',
  );
  bool privN = false;

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = roomPrivName != null ? roomPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
          scrollDirection: Axis.vertical,
          // shrinkWrap: true,
          onPageChanged: (i) {
            FocusManager.instance.primaryFocus?.unfocus();
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
          },
          physics: BouncingScrollPhysics(),
          children: [
            SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: sizedWidth * 0.03),
                child: Column(
                  children: [
                    Container(
                      width: sizedWidth * 0.85,
                      child: TextFormField(
                        controller: editPriv,
                        maxLength: 16,
                        showCursor: true,
                        cursorColor: AppColors.primary,
                        textDirection: TextDirection.rtl,
                        style: TextStyle(
                          color: AppColors.textPrimary,
                          fontSize: sized * 0.015,
                          fontWeight: FontWeight.w500,
                        ),
                        onChanged: (i) {
                          privN = true;
                        },
                        onEditingComplete: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                          if (editPriv.text == '' ||
                              editPriv.text == null ||
                              editPriv.text == 'X' ||
                              editPriv.text == 'x') {
                            editPriv.text =
                                roomPrivName != null ? roomPrivName : 'X';
                            privN = false;
                          } else if (editPriv.text != roomPrivName) {
                            for (var i = 0; i < editPriv.text.length; i++) {
                              if (arabic.contains(editPriv.text[i]) ||
                                  editPriv.text[i].isNumericOnly) {
                                privN = true;
                              } else {
                                editPriv.text =
                                    roomPrivName != null ? roomPrivName : 'X';
                                privN = false;
                                break;
                              }
                            }
                            if (privN) {
                              editPrivName(privN, editPriv.text);
                              privN = false;
                            }
                          }
                        },
                        decoration: InputDecoration(
                          hintText: 'اسم $place الخاص',
                          hintStyle: TextStyle(
                            color: AppColors.textHint,
                            fontSize: sized * 0.014,
                            fontWeight: FontWeight.normal,
                          ),
                          filled: true,
                          fillColor: AppColors.surface,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: sizedWidth * 0.04,
                            vertical: sizedHeight * 0.015,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.border,
                              width: 1.0,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.border,
                              width: 1.0,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: AppColors.primary,
                              width: 2.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    place == 'الغرفة'
                        ? containerPageOption(
                            content: Column(
                              children: [
                                MaterialButton(
                                  padding: EdgeInsets.symmetric(horizontal: 0),
                                  onPressed: () {
                                    editNames('show');
                                  },
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.arrow_back_ios,
                                          size: sized * 0.02,
                                          color: AppColors.subtitleColor,
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'اسماء و صفات $place العامة',
                                          textDirection: TextDirection.rtl,
                                          style: AppTypography.bodySmall
                                              .copyWith(
                                                  fontSize: sized * 0.015,
                                                  color:
                                                      AppColors.subtitleColor),
                                        ),
                                      ]),
                                ),
                                Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      IconButton(
                                        onPressed: () {
                                          editNames('edit');
                                        },
                                        icon: Icon(Icons.edit_outlined),
                                        color: AppColors.infoColor,
                                        iconSize: sized * 0.03,
                                      ),
                                      SizedBox(
                                        width: sizedWidth * 0.075,
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          editNames('del');
                                        },
                                        icon: Icon(Icons.delete_sweep_rounded),
                                        color: Color.fromARGB(255, 243, 33, 18),
                                        iconSize: sized * 0.032,
                                      ),
                                      SizedBox(
                                        width: sizedWidth * 0.075,
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          editNames('add');
                                        },
                                        icon: Icon(Icons.add_rounded),
                                        color: AppColors.primaryColor,
                                        iconSize: sized * 0.032,
                                      ),
                                    ]),
                              ],
                            ),
                          )
                        : Column(
                            children: [
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.menu_open_rounded,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'الكلمات الروتينية',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.more_time_rounded,
                                        color: AppColors.warningColor,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'المهام المجدولة',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.people,
                                        color: AppColors.warningColor,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'الاشخاص',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.only(
                                      right: controller.sizedWidth * 0.015),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    switchStyle(
                                        onChanged: (val) {},
                                        value: false,
                                        size: controller.sized * 0.001),
                                    Expanded(
                                        child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                          txtStyle(
                                              txt: 'التوقيت الصيفي',
                                              align: TextAlign.start),
                                          SizedBox(
                                            width: controller.sizedWidth * 0.01,
                                          ),
                                          iconStyle(
                                              icon: Icons.bedtime,
                                              color: AppColors.warningColor,
                                              size: controller.sized * 0.02),
                                        ]))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  onPressed: (() {}),
                                  child: Row(children: [
                                    iconStyle(
                                        icon: Icons.menu_open_rounded,
                                        size: controller.sized * 0.025),
                                    Expanded(
                                        child: txtStyle(
                                            txt: 'المنطقة : قلقيلية',
                                            align: TextAlign.start))
                                  ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sizedWidth * 0.025),
                                  onPressed: () {
                                    addRoom!();
                                  },
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.add_rounded,
                                          size: sized * 0.025,
                                          color: AppColors.primaryColor
                                              .withOpacity(0.6),
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'اضافه غرفة',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor
                                                  .withOpacity(0.6)),
                                        ),
                                      ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sizedWidth * 0.025),
                                  onPressed: nunDevices,
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.dns_rounded,
                                          size: sized * 0.025,
                                          color: AppColors.textColor
                                              .withOpacity(0.6),
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'الملحقات المتصلة',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor
                                                  .withOpacity(0.6)),
                                        ),
                                      ]),
                                ),
                              ),
                              SizedBox(
                                height: sizedHeight * 0.015,
                              ),
                              containerPageOption(
                                content: MaterialButton(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sizedWidth * 0.025),
                                  onPressed: () {},
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.wifi_rounded,
                                          size: sized * 0.025,
                                          color:
                                              Colors.lightBlue.withOpacity(0.6),
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'ادارة الشبكة و المستخدمين',
                                          textDirection: TextDirection.rtl,
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor
                                                  .withOpacity(0.6)),
                                        ),
                                      ]),
                                ),
                              ),
                            ],
                          ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    containerPageOption(
                        ver: 0.015,
                        content: Column(
                          children: [
                            Container(
                              height: sizedHeight * 0.2,
                              width: sizedWidth * 0.85,
                              child: image!.contains('com.example.zaen')
                                  ? Image.file(
                                      File(image),
                                      color: AppColors.subtitleColor
                                          .withOpacity(0.2),
                                      colorBlendMode: BlendMode.darken,
                                      fit: BoxFit.cover,
                                      filterQuality: FilterQuality.high,
                                    )
                                  : Image.asset(
                                      "$image",
                                      color: AppColors.subtitleColor
                                          .withOpacity(0.2),
                                      colorBlendMode: BlendMode.darken,
                                      fit: BoxFit.cover,
                                      filterQuality: FilterQuality.high,
                                    ),
                            ),
                            SizedBox(height: sizedHeight * 0.01),
                            MaterialButton(
                              padding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.01),
                              onPressed: asset,
                              child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.arrow_back_ios,
                                      size: sized * 0.02,
                                      color:
                                          AppColors.textColor.withOpacity(0.6),
                                    ),
                                    Expanded(
                                        child:
                                            SizedBox(width: double.infinity)),
                                    Text(
                                      'اختيار صوره من الموجود',
                                      textDirection: TextDirection.rtl,
                                      style: TextStyle(
                                          fontSize: sized * 0.015,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor
                                              .withOpacity(0.6)),
                                    ),
                                  ]),
                            ),
                            SizedBox(height: sizedHeight * 0.01),
                            MaterialButton(
                              padding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.01),
                              onPressed: roll,
                              child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.camera_rounded,
                                      size: sized * 0.02,
                                      color:
                                          AppColors.textColor.withOpacity(0.6),
                                    ),
                                    Expanded(
                                        child:
                                            SizedBox(width: double.infinity)),
                                    Text(
                                      'اختيار صوره من البوم الصور',
                                      textDirection: TextDirection.rtl,
                                      style: TextStyle(
                                          fontSize: sized * 0.015,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor
                                              .withOpacity(0.6)),
                                    ),
                                  ]),
                            ),
                            SizedBox(height: sizedHeight * 0.01),
                            MaterialButton(
                              padding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.01),
                              onPressed: camera,
                              child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.camera_alt_rounded,
                                      size: sized * 0.02,
                                      color:
                                          AppColors.textColor.withOpacity(0.6),
                                    ),
                                    Expanded(
                                        child:
                                            SizedBox(width: double.infinity)),
                                    Text(
                                      'التقاط صورة',
                                      textDirection: TextDirection.rtl,
                                      style: TextStyle(
                                          fontSize: sized * 0.015,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.textColor
                                              .withOpacity(0.6)),
                                    ),
                                  ]),
                            ),
                          ],
                        )),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    containerPageOption(
                      content: MaterialButton(
                          padding: EdgeInsets.zero,
                          onPressed: del,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              txtStyle(
                                  txt: 'إزالة $place',
                                  color: AppColors.errorColor),
                            ],
                          )),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                  ],
                ),
              ),
            )
          ]),
    ),
  );
}

Widget ACPage({
  String? id,
  var deviceState,
  bool swingState = false,
  var speedState = 2,
  var typeState = 1,
  var degree = 25.0,
  String? roomN,
  required bool connect,
  String? acPrivName,
  required Function() acRun,
  required Function() del,
  required Function() Dfavorite,
  required Function() editRoom,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required Function(bool?) switchState,
  required Function(double?) sliderState,
  required Function(int?) acTypeState,
  required Function() acSwingState,
  required Function(int?) acSpeedsState,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: acPrivName != 'x' ? acPrivName : 'X',
  );
  bool privN = false;

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = acPrivName != null ? acPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = acPrivName != null ? acPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
        scrollDirection: Axis.vertical,
        // shrinkWrap: true,
        onPageChanged: (i) {
          FocusManager.instance.primaryFocus?.unfocus();
          editPriv.text = acPrivName != null ? acPrivName : 'X';
        },
        physics: BouncingScrollPhysics(),
        children: connect == true &&
                client.connectionStatus!.state.name == 'connected'
            ? [
                Container(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: sizedWidth * 0.035),
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                          Directionality(
                            textDirection: TextDirection.rtl,
                            child: switchStyle(
                                value: deviceState, onChanged: switchState),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.bottomRight,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                      width: controller.sizedWidth * 0.44,
                                      padding: EdgeInsets.only(
                                          right: controller.sized * 0.01),
                                      child: FittedBox(
                                        alignment: Alignment.centerRight,
                                        fit: BoxFit.scaleDown,
                                        child: txtStyle(
                                            align: TextAlign.right,
                                            txt: acPrivName != 'x'
                                                ? acPrivName!
                                                : 'لا يوجد اسم'),
                                      )),
                                  Container(
                                      padding: EdgeInsets.only(
                                          left: controller.sizedWidth * 0.01),
                                      decoration: BoxDecoration(
                                          border: Border(
                                              left: BorderSide(
                                                  color: AppColors.textColor
                                                      .withOpacity(0.25),
                                                  width: 1.5))),
                                      child: txtStyle(
                                          align: TextAlign.right,
                                          txt: 'مكيف هواء',
                                          color: AppColors.textColor3)),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            width: sizedWidth * 0.01,
                          ),
                          iconStyle(
                            icon: Icons.ac_unit,
                            color: AppColors.warningColor,
                            size: sized * 0.035,
                          ),
                        ]),
                      ),
                      Column(
                        children: [
                          SizedBox(
                            height: sizedHeight * 0.02,
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                IconButton(
                                  onPressed: () {
                                    acTypeState(2);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: typeState == 2
                                      ? Color.fromARGB(255, 61, 182, 222)
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.052,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    acTypeState(1);
                                  },
                                  icon: Icon(Icons.ac_unit_rounded),
                                  color: typeState == 1
                                      ? Colors.cyan
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.052,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    acTypeState(0);
                                  },
                                  icon: Icon(Icons.wb_sunny_rounded),
                                  color: typeState == 0
                                      ? AppColors.warningColor
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.052,
                                ),
                              ]),
                          Directionality(
                            textDirection: TextDirection.rtl,
                            child: Container(
                              margin: EdgeInsets.only(top: sizedHeight * 0.025),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  SleekCircularSlider(
                                    min: 16,
                                    max: 30,
                                    initialValue: degree.toDouble(),
                                    appearance: CircularSliderAppearance(
                                        infoProperties: InfoProperties(
                                            modifier: (percentage) =>
                                                '${percentage.toInt()}°',
                                            mainLabelStyle: TextStyle(
                                                color: AppColors.textColor2,
                                                fontSize: sized * 0.05,
                                                fontWeight: FontWeight.bold)),
                                        size: (sized) * 0.16,
                                        customColors: CustomSliderColors(
                                            hideShadow: true,
                                            trackColor: AppColors.textColor2
                                                .withOpacity(0.2),
                                            progressBarColors: <Color>[
                                              Color(0xFF1AB600),
                                              Color(0xFF6DD400),
                                            ])),
                                    onChangeEnd: sliderState,
                                  )
                                ],
                              ),
                            ),
                          ),
                          Text(
                            'سرعه المروحة',
                            style: TextStyle(
                                color: AppColors.textColor3,
                                fontSize: sized * 0.012),
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Text(
                                  '1',
                                  style: TextStyle(
                                      color: speedState == 1
                                          ? Colors.blue.shade600
                                          : AppColors.textColor2
                                              .withOpacity(0.2),
                                      fontSize: sized * 0.015),
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(1);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: speedState == 1
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.05,
                                ),
                                Text(
                                  '2',
                                  style: TextStyle(
                                      color: speedState == 2
                                          ? Colors.blue.shade600
                                          : AppColors.textColor2
                                              .withOpacity(0.2),
                                      fontSize: sized * 0.015),
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(2);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: speedState == 2
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.05,
                                ),
                                Text(
                                  '3',
                                  style: TextStyle(
                                      color: speedState == 3
                                          ? Colors.blue.shade600
                                          : AppColors.textColor2
                                              .withOpacity(0.2),
                                      fontSize: sized * 0.015),
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(3);
                                  },
                                  icon: Icon(Icons.air_rounded),
                                  color: speedState == 3
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.045,
                                ),
                                IconButton(
                                  onPressed: () {
                                    acSpeedsState(4);
                                  },
                                  icon: Icon(Icons.autorenew_rounded),
                                  color: speedState == 4
                                      ? Colors.blue.shade600
                                      : AppColors.textColor2.withOpacity(0.2),
                                  iconSize: sized * 0.043,
                                ),
                              ]),
                          containerIconsOption(
                            color: AppColors.backgroundColor3.withOpacity(0.7),
                            padding: EdgeInsets.only(right: sizedWidth * 0.02),
                            margin: EdgeInsets.only(top: sizedHeight * 0.015),
                            content: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                IconButton(
                                  onPressed: acSwingState,
                                  icon: swingState
                                      ? Icon(Icons.check_box_rounded)
                                      : Icon(Icons
                                          .check_box_outline_blank_rounded),
                                  color: swingState
                                      ? AppColors.primaryColor
                                      : AppColors.current.onSurfaceVariant,
                                  iconSize: sized * 0.032,
                                ),
                                SizedBox(
                                  width: sizedWidth * 0.085,
                                ),
                                Text(
                                  'التأرجح',
                                  style: TextStyle(
                                      color: AppColors.textColor3,
                                      fontSize: sized * 0.015,
                                      fontWeight: FontWeight.bold),
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            height: sizedHeight * 0.025,
                          ),
                          Transform.rotate(
                            angle: 180 * math.pi / 180,
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              onPressed: acRun,
                              icon: Icon(
                                Icons.play_circle_fill_rounded,
                                size: sized * 0.06,
                                color: AppColors.primaryColor.withOpacity(0.85),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: acPrivName,
                    type: 'مكيف هواء')
              ]
            : [
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: acPrivName,
                    type: 'مكيف هواء',
                    connect: false)
              ],
      ),
    ),
  );
}

Widget TVPage({
  String? id,
  var deviceState = null,
  String? roomN,
  required Function(bool?) switchState,
  required Function() del,
  required Function() Dfavorite,
  required Function() editRoom,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required Function() tapOn_Tv_Icon,
  required Function() tapOn_VolumeUp,
  required Function() tapOn_VolumeDown,
  required Function() tapOn_ChUp,
  required Function() tapOn_ChDown,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_123,
  required Function() tapOn_menu,
  required Function(String?) tapOn_star,
  required List<Widget> tv_task,
  required bool connect,
  String? tvPrivName,
  required bool sil,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: tvPrivName != 'x' ? tvPrivName : 'X',
  );
  bool privN = false;
  PageController pageController = PageController();

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = tvPrivName! != 'x' ? tvPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = tvPrivName! != 'x' ? tvPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
        scrollDirection: Axis.vertical,
        // shrinkWrap: true,
        onPageChanged: (i) {
          FocusManager.instance.primaryFocus?.unfocus();
          editPriv.text = tvPrivName! != 'x' ? tvPrivName : 'X';
        },
        physics: BouncingScrollPhysics(),
        children: connect == true &&
                client.connectionStatus!.state.name == 'connected'
            ? [
                Container(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(
                        parent: NeverScrollableScrollPhysics()),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.035),
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            Directionality(
                              textDirection: TextDirection.rtl,
                              child: switchStyle(
                                  value: deviceState, onChanged: switchState),
                            ),
                            Expanded(
                              child: Container(
                                alignment: Alignment.bottomRight,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Container(
                                        width: controller.sizedWidth * 0.44,
                                        padding: EdgeInsets.only(
                                            right: controller.sized * 0.01),
                                        child: FittedBox(
                                          alignment: Alignment.centerRight,
                                          fit: BoxFit.scaleDown,
                                          child: txtStyle(
                                              align: TextAlign.right,
                                              txt: tvPrivName != 'x'
                                                  ? tvPrivName!
                                                  : 'لا يوجد اسم'),
                                        )),
                                    Container(
                                        padding: EdgeInsets.only(
                                            left: controller.sizedWidth * 0.01),
                                        decoration: BoxDecoration(
                                            border: Border(
                                                left: BorderSide(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.25),
                                                    width: 1.5))),
                                        child: txtStyle(
                                            align: TextAlign.right,
                                            txt: 'تلفاز',
                                            color: AppColors.textColor3)),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              width: sizedWidth * 0.01,
                            ),
                            iconStyle(
                              icon: Icons.tv_rounded,
                              color: AppColors.warningColor,
                              size: sized * 0.035,
                            ),
                          ]),
                        ),
                        SizedBox(
                          height: sizedHeight * 0.85,
                          child: PageView(
                              scrollDirection: Axis.horizontal,
                              physics: BouncingScrollPhysics(),
                              controller: pageController,
                              children: [
                                Column(
                                  children: [
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        containerIconsOption(
                                          content: Column(
                                            children: [
                                              GestureDetector(
                                                onTap: tapOn_ChUp,
                                                child: iconStyle(
                                                  icon: Icons
                                                      .arrow_drop_up_rounded,
                                                  size: sized * 0.06,
                                                  color: AppColors.textColor3,
                                                ),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.025,
                                              ),
                                              IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: tapOn_menu,
                                                iconSize: sized * 0.050,
                                                icon: iconStyle(
                                                    icon: Icons
                                                        .swap_horiz_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.025,
                                              ),
                                              GestureDetector(
                                                onTap: tapOn_ChDown,
                                                child: iconStyle(
                                                  icon: Icons
                                                      .arrow_drop_down_rounded,
                                                  size: sized * 0.06,
                                                  color: AppColors.textColor3,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        containerIconsOption(
                                          // padding: EdgeInsets.o,
                                          content: Column(
                                            children: [
                                              SizedBox(
                                                height: sizedHeight * 0.015,
                                              ),
                                              IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: () {
                                                  tapOn_star('show');
                                                },
                                                icon: iconStyle(
                                                    icon: Icons
                                                        .important_devices_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                width: sizedWidth * 0.2,
                                                height: sized * 0.027,
                                              ),
                                              IconButton(
                                                padding:
                                                    EdgeInsets.only(bottom: 5),
                                                onPressed: tapOn_123,
                                                icon: iconStyle(
                                                    icon: Icons.pin_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                height: sized * 0.027,
                                              ),
                                              IconButton(
                                                padding:
                                                    EdgeInsets.only(bottom: 5),
                                                onPressed: () {},
                                                icon: iconStyle(
                                                    icon: Icons
                                                        .subdirectory_arrow_left_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        containerIconsOption(
                                          content: Column(
                                            children: [
                                              GestureDetector(
                                                onTap: tapOn_VolumeUp,
                                                child: iconStyle(
                                                    icon: Icons
                                                        .arrow_drop_up_rounded,
                                                    size: sized * 0.06,
                                                    color:
                                                        AppColors.textColor3),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.03,
                                              ),
                                              IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: tapOn_VolumeMute,
                                                iconSize: sized * 0.045,
                                                icon: iconStyle(
                                                    icon: sil
                                                        ? Icons
                                                            .volume_up_rounded
                                                        : Icons
                                                            .volume_off_rounded,
                                                    size: controller.sized *
                                                        0.04),
                                              ),
                                              SizedBox(
                                                height: sizedHeight * 0.03,
                                              ),
                                              GestureDetector(
                                                onTap: tapOn_VolumeDown,
                                                child: iconStyle(
                                                    icon: Icons
                                                        .arrow_drop_down_rounded,
                                                    size: sized * 0.06,
                                                    color:
                                                        AppColors.textColor3),
                                              ),
                                            ],
                                          ),
                                        ),
                                        // SizedBox(
                                        //   width: 20,
                                        // ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        IconButton(
                                          padding: EdgeInsets.zero,
                                          onPressed: () {},
                                          icon: iconStyle(
                                              icon: Icons.exit_to_app_rounded,
                                              size: controller.sized * 0.04),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                          padding: EdgeInsets.only(bottom: 5),
                                          onPressed: () {},
                                          icon: iconStyle(
                                              icon: Icons.info_outline_rounded,
                                              size: controller.sized * 0.04),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                            padding: EdgeInsets.only(bottom: 5),
                                            onPressed: () {},
                                            icon: iconStyle(
                                                icon: Icons
                                                    .video_settings_rounded,
                                                size: controller.sized * 0.04)),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                        IconButton(
                                          padding: EdgeInsets.zero,
                                          onPressed: () {
                                            pageController.nextPage(
                                                duration:
                                                    Duration(milliseconds: 650),
                                                curve: Curves.ease);
                                          },
                                          icon: iconStyle(
                                              icon: Icons.fast_rewind_rounded,
                                              size: controller.sized * 0.04),
                                        ),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                            padding: EdgeInsets.only(bottom: 5),
                                            onPressed: () {},
                                            icon: iconStyle(
                                                icon: Icons.play_arrow_rounded,
                                                size: controller.sized * 0.04)),
                                        SizedBox(
                                          width: sizedWidth * 0.15,
                                        ),
                                        IconButton(
                                            padding: EdgeInsets.only(bottom: 5),
                                            onPressed: () {},
                                            icon: iconStyle(
                                              icon: Icons.fast_forward_rounded,
                                              size: controller.sized * 0.04,
                                            )),
                                        SizedBox(
                                          width: sizedWidth * 0.1,
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        IconButton(
                                          onPressed: () {
                                            FocusManager.instance.primaryFocus
                                                ?.unfocus();
                                            pageController.nextPage(
                                                duration:
                                                    Duration(milliseconds: 650),
                                                curve: Curves.ease);
                                          },
                                          icon: Icon(Icons.more_time_rounded),
                                          iconSize: sized * 0.045,
                                          color: AppColors.textColor
                                              .withOpacity(0.7),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                                Column(
                                  children: tv_task,
                                )
                              ]),
                        )
                      ],
                    ),
                  ),
                ),
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: tvPrivName,
                    tapOn_star: tapOn_star,
                    isTv: true,
                    type: 'تلفاز')
              ]
            : [
                pageSetting(
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    editPrivName: editPrivName,
                    roomN: roomN,
                    privName: tvPrivName,
                    tapOn_star: tapOn_star,
                    isTv: true,
                    type: 'تلفاز',
                    connect: false)
              ],
      ),
    ),
  );
}

Widget SWPage({
  String? id,
  var deviceState = null,
  String? roomN,
  required Function() del,
  required Function() Dfavorite,
  required Function(int?, String?) swType,
  required Function() editRoom,
  required Function(bool?, String?, int?) editPrivName,
  required Function(String?) editNames,
  required Function(String?, String?) editSwNames,
  String? SwPrivName,
  required Map swList,
  required Function(bool?) switchState,
  required Function(String?, bool?) switchTap,
  required bool connect,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv;

  if (pageVer == 1 || connect == false) {
    editPriv = TextEditingController(
      text: SwPrivName!.split('_')[0] != 'x' ? SwPrivName.split('_')[0] : 'X',
    );
  } else if (pageHor != null) {
    editPriv = TextEditingController(
      text: SwPrivName!.split('_')[pageHor! + 1] != 'x'
          ? SwPrivName.split('_')[pageHor! + 1]
          : 'X',
    );
  } else {
    editPriv = TextEditingController(
      text: SwPrivName!.split('_')[1] != 'x' ? SwPrivName.split('_')[1] : 'X',
    );
    pageHor = 0;
    pageVer = 0;
  }
  bool privN = false;
  return pageSlide(
    content: GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text =
                SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1] != 'x'
                    ? SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1]
                    : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text =
                    SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1] !=
                            'x'
                        ? SwPrivName.split('_')[pageVer == 1 ? 0 : pageHor! + 1]
                        : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(
                  privN, editPriv.text, pageVer == 1 ? 0 : pageHor! + 1);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
        scrollDirection: Axis.vertical,
        onPageChanged: (i) {
          pageVer = i;
          if (i == 1) {
            editPriv.text = SwPrivName.split('_')[0] != 'x'
                ? SwPrivName.split('_')[0]
                : 'X';
          } else {
            editPriv.text = SwPrivName.split('_')[pageHor! + 1] != 'x'
                ? SwPrivName.split('_')[pageHor! + 1]
                : 'X';
          }
          FocusManager.instance.primaryFocus?.unfocus();
        },
        physics: BouncingScrollPhysics(),
        children: connect == true &&
                client.connectionStatus!.state.name == 'connected'
            ? [
                Container(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: sizedWidth * 0.035),
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                          Directionality(
                            textDirection: TextDirection.rtl,
                            child: switchStyle(
                                onChanged: switchState, value: deviceState),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.bottomRight,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Container(
                                      width: controller.sizedWidth * 0.44,
                                      padding: EdgeInsets.only(
                                          right: controller.sized * 0.01),
                                      child: FittedBox(
                                        alignment: Alignment.centerRight,
                                        fit: BoxFit.scaleDown,
                                        child: txtStyle(
                                            align: TextAlign.right,
                                            txt: SwPrivName.split('_')[0] != 'x'
                                                ? SwPrivName.split('_')[0]
                                                : 'لا يوجد اسم'),
                                      )),
                                  Container(
                                      padding: EdgeInsets.only(
                                          left: controller.sizedWidth * 0.01),
                                      decoration: BoxDecoration(
                                          border: Border(
                                              left: BorderSide(
                                                  color: AppColors.textColor
                                                      .withOpacity(0.25),
                                                  width: 1.5))),
                                      child: txtStyle(
                                          align: TextAlign.right,
                                          txt: 'مفاتيح',
                                          color: AppColors.textColor3)),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            width: sizedWidth * 0.01,
                          ),
                          iconStyle(
                            icon: Icons.power_outlined,
                            color: AppColors.warningColor,
                            size: sized * 0.035,
                          ),
                        ]),
                      ),
                      Expanded(
                        // height: 160,
                        // padding:
                        //     const EdgeInsets.symmetric(horizontal: 10),
                        child: PageView(
                          // shrinkWrap: true,
                          scrollDirection: Axis.horizontal,

                          physics: BouncingScrollPhysics(),
                          onPageChanged: (val) {
                            pageHor = val;
                            editPriv.text =
                                SwPrivName.split('_')[val + 1] == 'x'
                                    ? 'X'
                                    : SwPrivName.split('_')[val + 1];
                          },
                          children: [
                            for (var i in swList.keys
                                .toList()
                                .getRange(0, swList.length - 4))
                              SingleChildScrollView(
                                padding: EdgeInsets.zero,
                                physics: BouncingScrollPhysics(
                                    parent: NeverScrollableScrollPhysics()),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(
                                        right: sized * 0.02,
                                        bottom: 0,
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            i,
                                            // 'ssss',
                                            textDirection: TextDirection.rtl,
                                            style: TextStyle(
                                                color: AppColors.textColor
                                                    .withOpacity(0.5),
                                                fontSize: sized * 0.02,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ],
                                      ),
                                    ),
                                    CircularPercentIndicator(
                                        radius: sized * 0.11,
                                        lineWidth: sized * 0.02,
                                        percent:
                                            swList[i]['state'] ? 0.968 : 0.0,
                                        // startAngle: 1,
                                        backgroundWidth: sized * 0.017,
                                        backgroundColor: swList[i]['state']
                                            ? Colors.transparent
                                            : AppColors.backgroundColor3,
                                        footer: Container(
                                          height: controller.sizedHight * 0.09,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: sizedWidth * 0.03),

                                          // margin: EdgeInsets.only(
                                          //     top: sizedHeight * 0.025),
                                          child: Container(
                                            width: sizedWidth * 0.85,
                                            child: TextFormField(
                                              textAlign: TextAlign.center,
                                              controller: editPriv,
                                              maxLength: 20,
                                              showCursor: true,
                                              cursorColor: AppColors.primary,
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                color: AppColors.textPrimary,
                                                fontSize: sized * 0.025,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              onChanged: (i) {
                                                privN = true;
                                              },
                                              onEditingComplete: () {
                                                FocusManager
                                                    .instance.primaryFocus
                                                    ?.unfocus();
                                                if (editPriv.text == '' ||
                                                    editPriv.text == null ||
                                                    editPriv.text == 'X' ||
                                                    editPriv.text == 'x') {
                                                  editPriv.text =
                                                      SwPrivName.split('_')[
                                                                  pageHor! +
                                                                      1] !=
                                                              'x'
                                                          ? SwPrivName.split(
                                                              '_')[pageHor! + 1]
                                                          : 'X';
                                                  privN = false;
                                                } else if (editPriv.text !=
                                                    SwPrivName.split(
                                                        '_')[pageHor! + 1]) {
                                                  for (var i = 0;
                                                      i < editPriv.text.length;
                                                      i++) {
                                                    if (arabic.contains(
                                                            editPriv.text[i]) ||
                                                        editPriv.text[i]
                                                            .isNumericOnly) {
                                                      privN = true;
                                                    } else {
                                                      editPriv.text = SwPrivName
                                                                      .split(
                                                                          '_')[
                                                                  pageHor! +
                                                                      1] !=
                                                              'x'
                                                          ? SwPrivName.split(
                                                              '_')[pageHor! + 1]
                                                          : 'X';
                                                      privN = false;
                                                      break;
                                                    }
                                                  }
                                                  if (privN) {
                                                    editPrivName(
                                                        privN,
                                                        editPriv.text,
                                                        pageHor! + 1);
                                                    privN = false;
                                                  }
                                                }
                                              },
                                              decoration: InputDecoration(
                                                hintText: 'اسم المفتاح',
                                                hintStyle: TextStyle(
                                                  color: AppColors.textHint,
                                                  fontSize: sized * 0.02,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                                filled: true,
                                                fillColor: AppColors.surface,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                  horizontal: sizedWidth * 0.04,
                                                  vertical: sizedHeight * 0.015,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.border,
                                                    width: 1.0,
                                                  ),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  borderSide: BorderSide(
                                                    color: AppColors.primary,
                                                    width: 2.0,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        center: IconButton(
                                          padding: EdgeInsets.zero,
                                          onPressed: () {
                                            switchTap(i, swList[i]['state']);
                                          },
                                          splashColor: Colors.transparent,
                                          icon: iconStyle(
                                            icon: swList[i]['type'] == 'LIGHT'
                                                ? Icons.lightbulb_rounded
                                                : swList[i]['type'] == 'VAN'
                                                    ? Icons.storm_outlined
                                                    : Icons.power_outlined,
                                            color: swList[i]['type'] ==
                                                        'LIGHT' &&
                                                    swList[i]['state']
                                                ? AppColors.warning
                                                : swList[i]['type'] == 'VAN' &&
                                                        swList[i]['state']
                                                    ? AppColors.info
                                                    : swList[i]['type'] ==
                                                                'SWITCH' &&
                                                            swList[i]['state']
                                                        ? AppColors.success
                                                        : AppColors.textHint,
                                            size: sized * 0.12,
                                          ),
                                        ),
                                        linearGradient: const LinearGradient(
                                            begin: Alignment.topRight,
                                            end: Alignment.bottomLeft,
                                            colors: <Color>[
                                              Color(0xFF6DD400),
                                              Color(0xFF1AB600),
                                            ]),
                                        rotateLinearGradient: true,
                                        circularStrokeCap:
                                            CircularStrokeCap.round),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: sizedWidth * 0.03),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: sizedHeight * 0.05,
                                          ),
                                          Container(
                                              decoration: BoxDecoration(
                                                border: Border(
                                                  top: BorderSide(
                                                      color: AppColors
                                                          .backgroundColor2,
                                                      width: 1.5),
                                                  // bottom: BorderSide(
                                                  //     color: AppColors.textColor
                                                  //         .withOpacity(
                                                  //             0.25),
                                                  //     width: 1.5),
                                                ),
                                              ),
                                              child: MaterialButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: () {
                                                  swType(
                                                      swList[i]['type'] ==
                                                              'SWITCH'
                                                          ? 0
                                                          : swList[i]['type'] ==
                                                                  'LIGHT'
                                                              ? 1
                                                              : 2,
                                                      i);
                                                },
                                                child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    children: [
                                                      iconStyle(
                                                        icon: Icons
                                                            .arrow_drop_down,
                                                        size: sized * 0.03,
                                                        color:
                                                            AppColors.textHint,
                                                      ),
                                                      Expanded(
                                                          child: SizedBox(
                                                              width: double
                                                                  .infinity)),
                                                      Row(
                                                        children: [
                                                          txtStyle(
                                                              txt:
                                                                  '${swList[i]['type'] == 'LIGHT' ? 'ضوء' : swList[i]['type'] == 'VAN' ? 'مروحه' : 'مفتاح'}',
                                                              size:
                                                                  sized * 0.015,
                                                              color: AppColors
                                                                  .textSecondary),
                                                          txtStyle(
                                                              txt:
                                                                  'نوع المفتاح : ',
                                                              size:
                                                                  sized * 0.015,
                                                              color: AppColors
                                                                  .textHint),
                                                        ],
                                                      ),
                                                    ]),
                                              )),
                                          // SizedBox(
                                          //   height: sizedHeight * 0.05,
                                          // ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.symmetric(
                                          horizontal: sizedWidth * 0.03),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(
                                              color: AppColors.backgroundColor2,
                                              width: 1.5),
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: sizedHeight * 0.02,
                                          ),
                                          MaterialButton(
                                            padding: EdgeInsets.zero,
                                            onPressed: () {
                                              editSwNames('show', i);
                                            },
                                            child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  iconStyle(
                                                    icon: Icons.arrow_drop_down,
                                                    size: sized * 0.03,
                                                    color: AppColors.textColor3,
                                                  ),
                                                  Expanded(
                                                      child: SizedBox(
                                                          width:
                                                              double.infinity)),
                                                  txtStyle(
                                                      txt:
                                                          'اسماء و صفات المفتاح العامة',
                                                      size: sized * 0.015,
                                                      color:
                                                          AppColors.textColor2),
                                                ]),
                                          ),
                                          Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                IconButton(
                                                  onPressed: () {
                                                    editSwNames('edit', i);
                                                  },
                                                  icon:
                                                      Icon(Icons.edit_outlined),
                                                  color: Colors.cyan,
                                                  iconSize: sized * 0.03,
                                                ),
                                                SizedBox(
                                                  width: sizedWidth * 0.075,
                                                ),
                                                IconButton(
                                                  onPressed: () {
                                                    editSwNames('del', i);
                                                  },
                                                  icon: Icon(Icons
                                                      .delete_sweep_rounded),
                                                  color: Color.fromARGB(
                                                      255, 243, 33, 18),
                                                  iconSize: sized * 0.032,
                                                ),
                                                SizedBox(
                                                  width: sizedWidth * 0.075,
                                                ),
                                                IconButton(
                                                  onPressed: () {
                                                    editSwNames('add', i);
                                                  },
                                                  icon: Icon(Icons.add_rounded),
                                                  color: AppColors.primaryColor,
                                                  iconSize: sized * 0.032,
                                                ),
                                              ]),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: sizedHeight * 0.05,
                                    )
                                  ],
                                ),
                              )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                pageSetting(
                    type: 'مفاتيح',
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    privName: SwPrivName.split('_')[0],
                    editPrivNameSw: editPrivName,
                    roomN: roomN,
                    isSw: true)
              ]
            : [
                pageSetting(
                    type: 'مفاتيح',
                    del: del,
                    id: id,
                    Dfavorite: Dfavorite,
                    editRoom: editRoom,
                    editNames: editNames,
                    privName: SwPrivName.split('_')[0],
                    editPrivNameSw: editPrivName,
                    roomN: roomN,
                    isSw: true,
                    connect: false)
              ],
      ),
    ),
  );
}

Widget ZainPage(
    {String? id,
    bool deviceState = false,
    String? roomN,
    required Function() Dfavorite,
    required Function() editRoom,
    required List alarmList,
    Function()? addAlarm,
    required Function(int? id, int? h, int? m, bool? isAM, List? days,
            bool? allday, bool? re)
        editAlarm,
    required bool connect,
    required String ZName,
    bool Zmain = false,
    required double sizedWidth,
    required double sizedHeight,
    required double sized,
    var conn}) {
  bool privN = false;
  if (_activePage == null) {
    _activePage = 0;
  }
  PageController _pageController = PageController(initialPage: _activePage!);

  // the index of the current page

  // this list holds all the pages
  // all of them are constructed in the very end of this file for readability

  return pageSlide(
    content: GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: StatefulBuilder(
        builder: ((context, setState) => PageView(
              scrollDirection: Axis.vertical,
              // shrinkWrap: true,
              onPageChanged: (i) {
                FocusManager.instance.primaryFocus?.unfocus();
                setState(() {
                  _activePage;
                });
              },
              physics: BouncingScrollPhysics(),
              children:
                  connect == true &&
                          client.connectionStatus!.state.name == 'connected'
                      ? [
                          Container(
                            child: SingleChildScrollView(
                              physics: BouncingScrollPhysics(
                                  parent: NeverScrollableScrollPhysics()),
                              child: Column(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: sizedWidth * 0.035),
                                    child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Zmain
                                              ? Padding(
                                                  padding: EdgeInsets.only(
                                                      right: sizedWidth * 0.01),
                                                  child: IconButton(
                                                    onPressed: () {},
                                                    icon: iconStyle(
                                                      icon: Icons.star_rounded,
                                                      color: AppColors
                                                          .warningColor,
                                                    ),
                                                  ))
                                              : Container(),
                                          Expanded(
                                            child: Container(
                                              alignment: Alignment.bottomRight,
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  Container(
                                                      width: controller
                                                              .sizedWidth *
                                                          0.43,
                                                      padding: EdgeInsets.only(
                                                          right:
                                                              controller.sized *
                                                                  0.01),
                                                      child: txtStyle(
                                                          align:
                                                              TextAlign.right,
                                                          txt: 'زين')),
                                                  Container(
                                                      padding: EdgeInsets.only(
                                                          left: controller
                                                                  .sizedWidth *
                                                              0.01),
                                                      decoration: BoxDecoration(
                                                          border: Border(
                                                              left: BorderSide(
                                                                  color: AppColors
                                                                      .textColor
                                                                      .withOpacity(
                                                                          0.25),
                                                                  width: 1.5))),
                                                      child: txtStyle(
                                                          align:
                                                              TextAlign.right,
                                                          txt: 'مساعد صوتي',
                                                          color: AppColors
                                                              .textColor3)),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            width: sizedWidth * 0.01,
                                          ),
                                          iconStyle(
                                            icon: Icons.flutter_dash_rounded,
                                            color: AppColors.warningColor,
                                          ),
                                        ]),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          GetBuilder<HomeController>(
                              builder: (controller) => Column(
                                    children: [
                                      // the page view
                                      SizedBox(
                                        height: controller.sizedHight * 0.724,
                                        child: PageView(
                                          controller: _pageController,
                                          physics: BouncingScrollPhysics(
                                              parent:
                                                  NeverScrollableScrollPhysics()),
                                          // onPageChanged: (int page) {
                                          //   setState(() {
                                          //     _activePage = page;
                                          //   });
                                          //   print(_activePage);
                                          // },
                                          children: [
                                            Column(
                                              children: [
                                                Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal:
                                                          sizedWidth * 0.05),
                                                  child: Row(children: [
                                                    IconButton(
                                                      onPressed: addAlarm,
                                                      icon:
                                                          Icon(Icons.add_alarm),
                                                      iconSize: sized * 0.03,
                                                      color: AppColors
                                                          .warningColor,
                                                    ),
                                                    Expanded(
                                                        child: Text('المنبه',
                                                            textDirection:
                                                                TextDirection
                                                                    .rtl,
                                                            style: TextStyle(
                                                              color: AppColors
                                                                  .textColor2,
                                                              fontSize:
                                                                  sized * 0.023,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            )))
                                                  ]),
                                                ),
                                                Divider(
                                                    color: AppColors.textColor2
                                                        .withOpacity(0.25)),
                                                Expanded(
                                                  child: SingleChildScrollView(
                                                    physics: ScrollPhysics(
                                                        parent:
                                                            BouncingScrollPhysics()),
                                                    child: Column(children: [
                                                      for (var alarm
                                                          in alarmList.reversed)
                                                        GestureDetector(
                                                          onLongPress: () {
                                                            print(
                                                                alarm['wday']);
                                                            print(
                                                                '222222222222222222222222222222222222');

                                                            editAlarm(
                                                                alarm['id'],
                                                                int.parse(alarm['clock']
                                                                        .split(
                                                                            ':')[
                                                                    0]),
                                                                int.parse(alarm['clock']
                                                                        .replaceAll(
                                                                            RegExp(
                                                                                r'\s?(AM|PM)'),
                                                                            '')
                                                                        .split(
                                                                            ':')[
                                                                    1]),
                                                                alarm['clock']
                                                                        .contains(
                                                                            'AM')
                                                                    ? true
                                                                    : false,
                                                                alarm['wday'] !=
                                                                        'None'
                                                                    ? [
                                                                        for (var d
                                                                            in alarm['wday'].split(' '))
                                                                          d == 'Sat'
                                                                              ? 'سبت'
                                                                              : d == 'Sun'
                                                                                  ? 'أحد'
                                                                                  : d == 'Mon'
                                                                                      ? 'إثنين'
                                                                                      : d == 'Tue'
                                                                                          ? 'ثلاثاء'
                                                                                          : d == 'Wed'
                                                                                              ? 'اربعاء'
                                                                                              : d == 'Thu'
                                                                                                  ? 'خميس'
                                                                                                  : 'جمعة'
                                                                      ]
                                                                    : [],
                                                                alarm['wday']
                                                                            .split(
                                                                                ' ')
                                                                            .length ==
                                                                        7
                                                                    ? true
                                                                    : false,
                                                                alarm['re'] ==
                                                                        'ON'
                                                                    ? true
                                                                    : false);
                                                          },
                                                          child: Column(
                                                            children: [
                                                              Directionality(
                                                                textDirection:
                                                                    TextDirection
                                                                        .rtl,
                                                                child:
                                                                    Container(
                                                                        padding: EdgeInsets.symmetric(
                                                                            horizontal: controller.sizedWidth *
                                                                                0.02),
                                                                        child:
                                                                            Column(
                                                                          children: [
                                                                            Row(
                                                                              crossAxisAlignment: CrossAxisAlignment.end,
                                                                              children: [
                                                                                Text(
                                                                                  alarm['clock'].replaceAll(RegExp(r'\s?(AM|PM)'), '').trim(),
                                                                                  textDirection: TextDirection.rtl,
                                                                                  style: TextStyle(
                                                                                    color: AppColors.warningColor,
                                                                                    fontSize: controller.sized * 0.04,
                                                                                  ),
                                                                                ),
                                                                                SizedBox(
                                                                                  width: controller.sizedWidth * 0.005,
                                                                                ),
                                                                                Expanded(
                                                                                  child: Padding(
                                                                                    padding: EdgeInsets.only(bottom: controller.sizedHight * 0.01, right: controller.sizedHight * 0.005),
                                                                                    child: Text(
                                                                                      alarm['clock'].contains('AM') ? 'صباحاً' : 'مسائاً',
                                                                                      textDirection: TextDirection.rtl,
                                                                                      style: TextStyle(color: AppColors.textColor2, fontSize: controller.sized * 0.015, fontWeight: FontWeight.bold),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                                Padding(
                                                                                    padding: EdgeInsets.all(controller.sized * 0.0035),
                                                                                    child: switchStyle(
                                                                                        onChanged: (val) async {
                                                                                          await conn.query("UPDATE Alarm SET state = ? WHERE id = ?", [
                                                                                            val! ? 'ON' : 'STOP',
                                                                                            alarm['id']
                                                                                          ]);
                                                                                          final builder = MqttClientPayloadBuilder();
                                                                                          builder.addString('1');
                                                                                          client.publishMessage('edit', MqttQos.atLeastOnce, builder.payload!);
                                                                                        },
                                                                                        size: controller.sized * 0.0008,
                                                                                        value: alarm['state'] == 'ON' ? true : false)),
                                                                              ],
                                                                            ),
                                                                            Padding(
                                                                              padding: EdgeInsets.symmetric(horizontal: sizedWidth * 0.01),
                                                                              child: Row(
                                                                                children: [
                                                                                  Text(
                                                                                    'سبت',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['سبت']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'أحد',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['أحد']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'إثنين',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['إثنين']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'ثلاثاء',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['ثلاثاء']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'اربعاء',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['اربعاء']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'خميس',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['خميس']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.02,
                                                                                  ),
                                                                                  Text(
                                                                                    'جمعة',
                                                                                    textDirection: TextDirection.rtl,
                                                                                    style: TextStyle(color: alarm['wday'].contains(weekDays['جمعة']) ? AppColors.warningColor : AppColors.backgroundColor2, fontSize: controller.sized * 0.012, fontWeight: FontWeight.bold),
                                                                                  ),
                                                                                  SizedBox(
                                                                                    width: sizedWidth * 0.05,
                                                                                  ),
                                                                                  Icon(
                                                                                    Icons.refresh_rounded,
                                                                                    size: sized * 0.018,
                                                                                    color: alarm['re'].contains('ON') ? AppColors.warningColor : AppColors.backgroundColor2,
                                                                                  )
                                                                                ],
                                                                              ),
                                                                            )
                                                                          ],
                                                                        )),
                                                              ),
                                                              Divider(
                                                                color: AppColors
                                                                    .textColor2
                                                                    .withOpacity(
                                                                        0.25),
                                                                endIndent:
                                                                    controller
                                                                            .sizedWidth *
                                                                        0.1,
                                                              )
                                                            ],
                                                          ),
                                                        )
                                                    ]),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const PageTwo(),
                                            const PageThree()
                                          ],
                                        ),
                                      ),
                                      // Display the dots indicator

                                      Divider(
                                          color: AppColors.textColor2
                                              .withOpacity(0.25)),
                                      Container(
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: List<Widget>.generate(
                                              3,
                                              (index) => Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal:
                                                                sizedWidth *
                                                                    0.1),
                                                    child: InkWell(
                                                        onTap: () {
                                                          _pageController.animateToPage(
                                                              index,
                                                              duration:
                                                                  const Duration(
                                                                      milliseconds:
                                                                          3),
                                                              curve: Curves
                                                                  .easeIn);
                                                          setState(() {
                                                            _activePage = index;
                                                          });
                                                        },
                                                        child: Column(
                                                          children: [
                                                            Icon(
                                                              index == 0
                                                                  ? Icons
                                                                      .alarm_rounded
                                                                  : index == 1
                                                                      ? Icons
                                                                          .calendar_today
                                                                      : Icons
                                                                          .timer_rounded,
                                                              size: _activePage == index
                                                                  ? controller
                                                                          .sized *
                                                                      0.033
                                                                  : controller
                                                                          .sized *
                                                                      0.03,
                                                              color: _activePage ==
                                                                      index
                                                                  ? AppColors
                                                                      .warningColor
                                                                  : AppColors
                                                                      .textColor3,
                                                            ),
                                                            Text(
                                                              index == 0
                                                                  ? 'منبه'
                                                                  : index == 1
                                                                      ? 'تذكيرات'
                                                                      : 'مـؤقت',
                                                              style: TextStyle(
                                                                  fontSize: controller
                                                                          .sized *
                                                                      0.012,
                                                                  color: _activePage ==
                                                                          index
                                                                      ? AppColors
                                                                          .warningColor
                                                                      : AppColors
                                                                          .textColor3,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold),
                                                            )
                                                          ],
                                                        )
                                                        // CircleAvatar(
                                                        //   radius: _activePage == index ? controller.sized * 0.004 : controller.sized * 0.0035,
                                                        //   // check if a dot is connected to the current page
                                                        //   // if true, give it a different color
                                                        //   backgroundColor: _activePage == index ? AppColors.warningColor : AppColors.textColor2.withOpacity(0.5),
                                                        // ),
                                                        ),
                                                  )),
                                        ),
                                      ),
                                    ],
                                  )),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: sizedWidth * 0.03),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: sizedHeight * 0.01,
                                ),
                                MaterialButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: () {
                                    roomN = editRoom();
                                    print(roomN);
                                  },
                                  child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.arrow_drop_down,
                                          size: sized * 0.03,
                                          color: AppColors.textColor3,
                                        ),
                                        Expanded(
                                            child: SizedBox(
                                                width: double.infinity)),
                                        Text(
                                          'الغرفة : $roomN',
                                          style: TextStyle(
                                              fontSize: sized * 0.015,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.textColor3),
                                        ),
                                      ]),
                                ),
                                SizedBox(
                                  height: sizedHeight * 0.05,
                                ),
                                containerPageOption(
                                  content: MaterialButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: Dfavorite,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          iconStyle(
                                              icon: Icons.favorite,
                                              size: controller.sized * 0.02,
                                              color: controller.favorite
                                                      .contains(id)
                                                  ? AppColors.errorColor
                                                  : AppColors.textColor3),
                                          Expanded(
                                              child: SizedBox(
                                                  width: double.infinity)),
                                          txtStyle(
                                              txt: 'الملحقات المفضله',
                                              color: AppColors.textColor2),
                                        ],
                                      )),
                                ),
                                SizedBox(
                                  height: controller.sizedHight * 0.015,
                                ),
                              ],
                            ),
                          )
                        ]
                      : [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: sizedWidth * 0.035),
                            child: Column(
                              children: [
                                Row(mainAxisSize: MainAxisSize.min, children: [
                                  Text(
                                    'غير متصل',
                                    textDirection: TextDirection.rtl,
                                    style: TextStyle(
                                        color: AppColors.errorColor,
                                        fontSize: sized * 0.013,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Expanded(
                                    child: Container(
                                      margin: EdgeInsets.zero,
                                      // padding: EdgeInsets.symmetric(horizontal: 5),
                                      // color: Colors.blueGrey.shade600,
                                      alignment: Alignment.bottomRight,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Container(
                                              padding: EdgeInsets.only(
                                                  right: sized * 0.01),
                                              child: Text(
                                                'زين',
                                                textDirection:
                                                    TextDirection.rtl,
                                                style: TextStyle(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.8),
                                                    fontSize: sized * 0.013,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              )),
                                          Container(
                                            padding: EdgeInsets.only(
                                                left: sizedWidth * 0.01),
                                            decoration: BoxDecoration(
                                                border: Border(
                                                    left: BorderSide(
                                                        color: AppColors
                                                            .textColor
                                                            .withOpacity(0.25),
                                                        width: 1.5))),
                                            child: Text(
                                              'مساعد صوتي',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: AppColors.textColor
                                                      .withOpacity(0.8),
                                                  fontSize: sized * 0.013,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: sizedWidth * 0.01,
                                  ),
                                  Icon(
                                    Icons.flutter_dash_rounded,
                                    color: AppColors.warningColor,
                                    size: sized * 0.035,
                                  ),
                                ]),
                                SizedBox(
                                  height: sizedHeight * 0.05,
                                ),
                                SizedBox(
                                  height: sizedHeight * 0.05,
                                ),
                              ],
                            ),
                          )
                        ],
            )),
      ),
    ),
  );
}

class PageOne extends StatelessWidget {
  const PageOne({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
    );
  }
}

// Page Two
class PageTwo extends StatelessWidget {
  const PageTwo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
    );
  }
}

// Page Three
class PageThree extends StatelessWidget {
  const PageThree({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
    );
  }
}
