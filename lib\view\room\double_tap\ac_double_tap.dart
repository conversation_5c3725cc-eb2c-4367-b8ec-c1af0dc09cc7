import 'package:flutter/cupertino.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:zaen/models/pages.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/commands/ac.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/view/room/double_tap/del.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/view/room/double_tap/edit_names.dart';
import 'package:zaen/view/room/double_tap/edit_room.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Future<dynamic> acDoubleTap({
  required var context,
  required var i,
}) async {
  final conn = await MySqlConnection.connect(ConnectionSettings(
      host: controller.hostZain.value,
      // port: 80,
      user: 'root',
      db: 'zain',
      password: 'zain',
      characterSet: CharacterSet.UTF8));
  var appDB = await openDatabase('${controller.system}.db', version: 3);

  Map rooms = {};
  print(11111111111111111);

  var selectroom = 0;
  var selectname = 0;

  for (var r = 0; r < controller.rooms.keys.toList().length; r++) {
    rooms[controller.rooms[controller.rooms.keys.toList()[r]]['id']] =
        controller.rooms[controller.rooms.keys.toList()[r]]['privName'];
    if (roomId == controller.rooms[controller.rooms.keys.toList()[r]]['id']) {
      selectroom = r;
    }
  }
  print(rooms);
  showBottomSheet(
      enableDrag: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return GetBuilder<HomeController>(
          builder: (controller) => ACPage(
            id: i['id'],
            sizedWidth: controller.sizedWidth,
            sizedHeight: controller.sizedHight,
            sized: controller.sized,
            connect: controller.devices[i['id']],
            // acId: i['id'],
            deviceState: controller.rooms[rooms.keys.toList()[selectroom]]
                ['devices'][i['id']]['state'],
            degree: controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                [i['id']]['degree'],
            typeState: controller.rooms[rooms.keys.toList()[selectroom]]
                        ['devices'][i['id']]['type'] ==
                    'تدفئه'
                ? 0
                : controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                            [i['id']]['type'] ==
                        'تبريد'
                    ? 1
                    : 2,
            roomN: rooms[rooms.keys.toList()[selectroom]],
            speedState: controller.rooms[rooms.keys.toList()[selectroom]]
                ['devices'][i['id']]['speed'],
            swingState: controller.rooms[rooms.keys.toList()[selectroom]]
                ['devices'][i['id']]['swing'],
            acPrivName: controller.rooms[rooms.keys.toList()[selectroom]]
                ['devices'][i['id']]['priv'],
            del: () {
              del(appDB: appDB, i: i, context: context, conn: conn);
            },
            Dfavorite: () {
              Dfavorite(
                  context: context, device: i['id'], appDB: appDB, state: true);
            },
            acRun: () {
              if (client.connectionStatus!.state.name == 'connected') {
                print('RUN ' +
                    i['degree'].toInt().toString() +
                    ' ' +
                    i['type'].toString() +
                    ' van ' +
                    i['speed'].toString());
                if (controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                        [i['id']]['state'] ==
                    false) {
                  switchTap('state', i['state'], i['id']);
                }
                roomState = false;
                for (var j in controller
                    .rooms[rooms.keys.toList()[selectroom]]['devices'].values) {
                  if (j['state'] == true) {
                    roomState = true;
                  }
                  // setState(() {

                  // });
                }
                controller.rooms[rooms.keys.toList()[selectroom]]['state'] =
                    roomState;
                if (roomState == true) {
                  controller.homeState = true;
                } else {
                  controller.homeState = false;
                  for (var i in controller.rooms.values) {
                    if (i['state'] == true) {
                      controller.homeState = true;
                    }
                  }
                }
                final builder = MqttClientPayloadBuilder();
                builder.addString(i['id'] +
                    ' AC RUN ' +
                    (i['type'].toString() == 'مروحه'
                        ? 'X'
                        : i['degree'].toInt().toString()) +
                    ' VAN ' +
                    i['speed'].toString() +
                    ' ' +
                    (i['type'].toString() == 'تبريد'
                        ? 'AC'
                        : i['type'].toString() == 'تدفئه'
                            ? 'HEAT'
                            : 'VAN'));
                client.publishMessage(controller.homeId + "/app/zain",
                    MqttQos.atLeastOnce, builder.payload!);

                controller.update();
              }
            },
            editRoom: () {
              x() async {
                var s = await editRoom(
                    context: context,
                    selectroom: selectroom,
                    rooms: rooms,
                    i: i);
                s = await s.toString();
                if (selectroom != int.parse(s)) {
                  selectroom = await int.parse(s);
                  final builder = MqttClientPayloadBuilder();
                  builder.addString('1');
                  await client.publishMessage(
                      'edit', MqttQos.atLeastOnce, builder.payload!);
                  await builder.clear();
                  await builder.addString('re');
                  await client.publishMessage(
                      i['id'], MqttQos.atLeastOnce, builder.payload!);
                  Navigator.of(context).pop();
                }
              }

              x();
            },
            editNames: (string) async {
              selectname = await editNames(
                  context: context,
                  conn: conn,
                  selectname: selectname,
                  string: string,
                  i: i);
            },

            editPrivName: (privN, priv) async {
              print(privN!);
              if (privN) {
                print(123);
                s() async {
                  var myHome = await appDB.rawQuery('SELECT * FROM devices');

                  print(myHome);

                  await appDB.transaction((txn) async {
                    print(12345);
                    await txn.rawUpdate(
                        'UPDATE devices SET name = ? WHERE id = ?',
                        [priv, i['id']]);
                    print(123456);
                  });

                  myHome = await appDB.rawQuery('SELECT * FROM devices');
                  print(myHome);
                }

                await s();
                controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                    [i['id']]['priv'] = priv;
                // i['priv']=priv;
                privN = false;
              } else {
                priv = i['priv'] != null ? i['priv'] : 'X';
              }
              controller.update();
            },
            switchState: (val) {
              commandAc(val!, i, rooms.keys.toList()[selectroom]);
              // if (client.connectionStatus!.state.name == 'connected') {
              //   print('يرسل الامر الى السيرفر ');
              //   // getRoomDevices();
              //   switchTap('state', i['state'], i['id']);
              //   roomState = false;
              //   for (var j in controller
              //       .rooms[rooms.keys.toList()[selectroom]]['devices'].values) {
              //     if (j['state'] == true) {
              //       roomState = true;
              //     }
              //     // setState(() {

              //     // });
              //   }
              //   controller.rooms[rooms.keys.toList()[selectroom]]['state'] =
              //       roomState;
              //   if (val == true) {
              //     controller.homeState = true;
              //   } else {
              //     controller.homeState = false;
              //     for (var i in controller.rooms.values) {
              //       if (i['state'] == true) {
              //         controller.homeState = true;
              //       }
              //     }
              //   }
              //   final builder = MqttClientPayloadBuilder();

              //   if (val == true) {
              //     builder.addString(i['id'] +
              //         ' AC RUN ' +
              //         i['degree'].toInt().toString() +
              //         ' VAN ' +
              //         i['speed'].toString() +
              //         ' ' +
              //         (i['type'].toString() == 'تبريد'
              //             ? 'AC'
              //             : i['type'].toString() == 'تدفئه'
              //                 ? 'HEAT'
              //                 : 'VAN'));
              //   } else {
              //     builder.addString(i['id'] + ' AC OFF');
              //   }
              //   client.publishMessage(controller.homeId + "/app/zain",
              //       MqttQos.atLeastOnce, builder.payload!);

              //   controller.update();
              // }
            },
            sliderState: (val) {
              // setState(() {
              if (client.connectionStatus!.state.name == 'connected') {
                controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                    [i['id']]['degree'] = val;
                // });
                controller.update();
              }
            },
            acTypeState: (val) {
              if (client.connectionStatus!.state.name == 'connected') {
                print(val);
                // setState(() {
                controller.rooms[rooms.keys
                        .toList()[selectroom]]['devices'][i['id']]['type'] =
                    val == 0
                        ? 'تدفئه'
                        : val == 1
                            ? 'تبريد'
                            : 'مروحه';
                // });
                controller.update();
              }
            },
            acSwingState: () {
              if (client.connectionStatus!.state.name == 'connected') {
                switchTap('swing', i['swing'], i['id']);
                controller.update();
              }
            },
            acSpeedsState: (val) {
              // setState(() {
              if (client.connectionStatus!.state.name == 'connected') {
                controller.rooms[rooms.keys.toList()[selectroom]]['devices']
                    [i['id']]['speed'] = val;
                // });
                controller.update();
              }
            },
          ),
        );
      });
}
