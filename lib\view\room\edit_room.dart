import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

editRoom({context, selectroom, rooms, i}) async {
  if (client.connectionStatus!.state.name == 'connected') {
    await AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.backgroundColor3,
      body: Padding(
        padding: EdgeInsets.all(controller.sized * 0.01),
        child: Column(
          children: [
            SizedBox(
              height: controller.sizedHight * 0.25,
              child: CupertinoPicker(
                squeeze: 1.2,
                // looping: true,
                useMagnifier: true,
                magnification: 1.35,
                scrollController:
                    FixedExtentScrollController(initialItem: selectroom),
                itemExtent: controller.sizedHight * 0.05, //height of each item

                backgroundColor: Colors.transparent,
                children: <Widget>[
                  for (var c in rooms.keys)
                    Center(
                      child: Text(
                        rooms[c],
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: AppColors.textColor2,
                            fontSize: controller.sized * 0.017,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                ],
                onSelectedItemChanged: (int index) {
                  selectroom = index;
                },
              ),
            ),
            Container(
              width: double.infinity,
              child: submitButtom(
                onPressed: () async {
                  Results Rooms = await conn.query(
                      'update Devices set RoomS=? where id=?',
                      [rooms.keys.toList()[selectroom], i['id']]);
                },
                // btnCancelOnPress:
                //     () {},
              ),
            )
          ],
        ),
      ),
    ).show();
    return selectroom;
  }
}
