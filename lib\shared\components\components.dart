import 'dart:io';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:toggle_switch/toggle_switch.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/settings/settings.dart';
import '../../controller/controller.dart';

// Widget defaultButton({
//   double width = double.infinity,
//   colorBottun = AppColors.primaryColor,
//   bool isUpperCase = true,
//   double radius = 3.0,
//   required Function() function,
//   required String text,
// }) =>
//     Container(
//       width: width,
//       height: 50.0,
//       child: MaterialButton(
//         onPressed: function,
//         child: Text(
//           isUpperCase ? text.toUpperCase() : text,
//           style: const TextStyle(
//             color: AppColors.textColor,
//           ),
//         ),
//       ),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(
//           radius,
//         ),
//         color: AppColors.primaryColor,
//       ),
//     );

// Widget defaultFormField({
//   required TextEditingController controller,
//   TextInputType? type,
//   Function(String)? onSubmit,
//   Function(String)? onChange,
//   bool isPassword = false,
//   required String? Function(String?)? validate,
//   required String label,
//   required IconData prefix,
//   IconData? suffix,
//   Function()? suffixPressed,
//   colorIcon = AppColors.primaryColor,
// }) =>
//     TextFormField(
//       controller: controller,
//       keyboardType: type,
//       obscureText: isPassword,
//       onFieldSubmitted: onSubmit,
//       onChanged: onChange,
//       validator: validate,
//       decoration: InputDecoration(
//         labelText: label,
//         prefixIcon: Icon(
//           prefix,
//           color: colorIcon,
//         ),
//         suffixIcon: suffix != null
//             ? IconButton(
//                 icon: Icon(
//                   suffix,
//                   color: colorIcon,
//                 ),
//                 onPressed: suffixPressed,
//               )
//             : null,
//         border: const OutlineInputBorder(),
//       ),
//     );

int? _activePage;

// ===== حركات الانتقال بين الصفحات =====

// حركة انزلاق من اليمين إلى اليسار
class SlideRightToLeftRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideRightToLeftRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

// حركة انزلاق من اليسار إلى اليمين
class SlideLeftToRightRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideLeftToRightRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(-1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

// حركة انزلاق من الأسفل إلى الأعلى
class SlideBottomToTopRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideBottomToTopRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 500),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: FadeTransition(
                opacity: animation.drive(fadeTween),
                child: child,
              ),
            );
          },
        );
}

// حركة تكبير مع شفافية
class ScaleRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  ScaleRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 600),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const curve = Curves.elasticOut;

            var scaleTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: curve),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return ScaleTransition(
              scale: animation.drive(scaleTween),
              child: FadeTransition(
                opacity: animation.drive(fadeTween),
                child: child,
              ),
            );
          },
        );
}

// حركة دوران مع تكبير
class RotationScaleRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  RotationScaleRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 800),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var rotationTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.elasticOut),
            );

            var scaleTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeOutBack),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return RotationTransition(
              turns: animation.drive(rotationTween),
              child: ScaleTransition(
                scale: animation.drive(scaleTween),
                child: FadeTransition(
                  opacity: animation.drive(fadeTween),
                  child: child,
                ),
              ),
            );
          },
        );
}

// حركة انزلاق مع تأثير العمق
class SlideWithDepthRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideWithDepthRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 500),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;

            var slideTween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: Curves.easeInOutCubic),
            );

            var scaleTween = Tween(begin: 0.8, end: 1.0).chain(
              CurveTween(curve: Curves.easeOutCubic),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return SlideTransition(
              position: animation.drive(slideTween),
              child: ScaleTransition(
                scale: animation.drive(scaleTween),
                child: FadeTransition(
                  opacity: animation.drive(fadeTween),
                  child: child,
                ),
              ),
            );
          },
        );
}

// حركة مخصصة للدخول إلى الغرف
class RoomEntranceRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  RoomEntranceRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 700),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // حركة انزلاق من اليمين مع تأثير الباب المفتوح
            const slideBegin = Offset(1.0, 0.0);
            const slideEnd = Offset.zero;

            // حركة تكبير تدريجي
            const scaleBegin = 0.7;
            const scaleEnd = 1.0;

            // حركة دوران خفيف
            const rotationBegin = 0.05;
            const rotationEnd = 0.0;

            var slideTween = Tween(begin: slideBegin, end: slideEnd).chain(
              CurveTween(curve: Curves.easeOutCubic),
            );

            var scaleTween = Tween(begin: scaleBegin, end: scaleEnd).chain(
              CurveTween(curve: Curves.elasticOut),
            );

            var rotationTween =
                Tween(begin: rotationBegin, end: rotationEnd).chain(
              CurveTween(curve: Curves.easeOutBack),
            );

            var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: Curves.easeIn),
            );

            return SlideTransition(
              position: animation.drive(slideTween),
              child: RotationTransition(
                turns: animation.drive(rotationTween),
                child: ScaleTransition(
                  scale: animation.drive(scaleTween),
                  child: FadeTransition(
                    opacity: animation.drive(fadeTween),
                    child: child,
                  ),
                ),
              ),
            );
          },
        );
}

// حركة مخصصة أخرى للغرف - تأثير الباب المنزلق
class SlidingDoorRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlidingDoorRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 600),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return AnimatedBuilder(
              animation: animation,
              builder: (context, child) {
                // تأثير الباب المنزلق من اليمين
                final slideValue =
                    Curves.easeInOutCubic.transform(animation.value);
                final scaleValue = Curves.elasticOut.transform(animation.value);
                final fadeValue = Curves.easeIn.transform(animation.value);

                return Transform.translate(
                  offset: Offset(
                      (1 - slideValue) * MediaQuery.of(context).size.width, 0),
                  child: Transform.scale(
                    scale: 0.8 + (0.2 * scaleValue),
                    child: Opacity(
                      opacity: fadeValue,
                      child: ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(25 * (1 - slideValue)),
                          bottomLeft: Radius.circular(25 * (1 - slideValue)),
                        ),
                        child: child,
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
}

// ===== دوال مساعدة للتنقل مع الحركات =====

// التنقل مع حركة انزلاق من اليمين
void navigateWithSlideRight(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideRightToLeftRoute(page: page));
}

// التنقل مع حركة انزلاق من اليسار
void navigateWithSlideLeft(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideLeftToRightRoute(page: page));
}

// التنقل مع حركة انزلاق من الأسفل
void navigateWithSlideUp(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideBottomToTopRoute(page: page));
}

// التنقل مع حركة تكبير
void navigateWithScale(BuildContext context, Widget page) {
  Navigator.of(context).push(ScaleRoute(page: page));
}

// التنقل مع حركة دوران وتكبير
void navigateWithRotation(BuildContext context, Widget page) {
  Navigator.of(context).push(RotationScaleRoute(page: page));
}

// التنقل مع حركة انزلاق مع عمق
void navigateWithDepth(BuildContext context, Widget page) {
  Navigator.of(context).push(SlideWithDepthRoute(page: page));
}

// التنقل مع حركة دخول الغرفة المخصصة
void navigateToRoom(BuildContext context, Widget page) {
  Navigator.of(context).push(RoomEntranceRoute(page: page));
}

// حركة بسيطة وآمنة للغرف
void navigateToRoomSafe(BuildContext context, Widget page) {
  Navigator.of(context).push(
    PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    ),
  );
}

// التنقل مع حركة الباب المنزلق
void navigateWithSlidingDoor(BuildContext context, Widget page) {
  Navigator.of(context).push(SlidingDoorRoute(page: page));
}

// دالة مخصصة للتنقل إلى الغرف مع خيارات متعددة
void navigateToRoomWithStyle(
  BuildContext context,
  Widget page, {
  RoomTransitionStyle style = RoomTransitionStyle.entrance,
}) {
  switch (style) {
    case RoomTransitionStyle.entrance:
      Navigator.of(context).push(RoomEntranceRoute(page: page));
      break;
    case RoomTransitionStyle.slidingDoor:
      Navigator.of(context).push(SlidingDoorRoute(page: page));
      break;
    case RoomTransitionStyle.depth:
      Navigator.of(context).push(SlideWithDepthRoute(page: page));
      break;
  }
}

enum RoomTransitionStyle {
  entrance,
  slidingDoor,
  depth,
}

// دالة عامة للتنقل مع اختيار نوع الحركة
enum PageTransitionType {
  slideRight,
  slideLeft,
  slideUp,
  scale,
  rotation,
  depth,
  roomEntrance,
  slidingDoor,
}

void navigateWithAnimation(
  BuildContext context,
  Widget page, {
  PageTransitionType type = PageTransitionType.slideRight,
  Duration? duration,
}) {
  PageRouteBuilder route;

  switch (type) {
    case PageTransitionType.slideRight:
      route = SlideRightToLeftRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 400),
      );
      break;
    case PageTransitionType.slideLeft:
      route = SlideLeftToRightRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 400),
      );
      break;
    case PageTransitionType.slideUp:
      route = SlideBottomToTopRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 500),
      );
      break;
    case PageTransitionType.scale:
      route = ScaleRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 600),
      );
      break;
    case PageTransitionType.rotation:
      route = RotationScaleRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 800),
      );
      break;
    case PageTransitionType.depth:
      route = SlideWithDepthRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 500),
      );
      break;
    case PageTransitionType.roomEntrance:
      route = RoomEntranceRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 700),
      );
      break;
    case PageTransitionType.slidingDoor:
      route = SlidingDoorRoute(
        page: page,
        duration: duration ?? const Duration(milliseconds: 600),
      );
      break;
  }

  Navigator.of(context).push(route);
}

// دوال مساعدة للاستخدام مع GetX
class AnimatedNavigation {
  // التنقل مع حركة انزلاق من اليمين
  static Future<T?> toWithSlideRight<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.rightToLeft,
          duration: const Duration(milliseconds: 400),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة انزلاق من اليسار
  static Future<T?> toWithSlideLeft<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.leftToRight,
          duration: const Duration(milliseconds: 400),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة انزلاق من الأسفل
  static Future<T?> toWithSlideUp<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.downToUp,
          duration: const Duration(milliseconds: 500),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة تكبير
  static Future<T?> toWithScale<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.zoom,
          duration: const Duration(milliseconds: 600),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة شفافية
  static Future<T?> toWithFade<T>(Widget page) {
    return Get.to<T>(
          () => page,
          transition: Transition.fade,
          duration: const Duration(milliseconds: 400),
        ) ??
        Future.value(null);
  }

  // التنقل مع حركة مخصصة
  static Future<T?> toWithCustomAnimation<T>(
    Widget page, {
    Transition? transition,
    Duration? duration,
    Curve? curve,
  }) {
    return Get.to<T>(
          () => page,
          transition: transition ?? Transition.rightToLeft,
          duration: duration ?? const Duration(milliseconds: 400),
          curve: curve ?? Curves.easeInOut,
        ) ??
        Future.value(null);
  }

  // التنقل باستخدام اسم الصفحة مع حركة
  static Future<T?> toNamedWithAnimation<T>(
    String routeName, {
    dynamic arguments,
  }) {
    return Get.toNamed<T>(
          routeName,
          arguments: arguments,
        ) ??
        Future.value(null);
  }
}

// ===== PageTransitionBuilder مخصص للتطبيق =====

class CustomPageTransitionBuilder extends PageTransitionsBuilder {
  @override
  Widget buildTransitions<T extends Object?>(
    PageRoute<T> route,
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // حركة انزلاق من اليمين مع تأثير العمق
    const begin = Offset(1.0, 0.0);
    const end = Offset.zero;
    const curve = Curves.easeInOutCubic;

    var slideTween = Tween(begin: begin, end: end).chain(
      CurveTween(curve: curve),
    );

    var scaleTween = Tween(begin: 0.85, end: 1.0).chain(
      CurveTween(curve: curve),
    );

    var fadeTween = Tween(begin: 0.0, end: 1.0).chain(
      CurveTween(curve: Curves.easeIn),
    );

    return SlideTransition(
      position: animation.drive(slideTween),
      child: ScaleTransition(
        scale: animation.drive(scaleTween),
        child: FadeTransition(
          opacity: animation.drive(fadeTween),
          child: child,
        ),
      ),
    );
  }
}

// ===== حركات انتقال للـ PageView =====

class AnimatedPageView extends StatefulWidget {
  final List<Widget> children;
  final PageController? controller;
  final ValueChanged<int>? onPageChanged;
  final ScrollPhysics? physics;
  final Axis scrollDirection;
  final Duration animationDuration;
  final Curve animationCurve;

  const AnimatedPageView({
    Key? key,
    required this.children,
    this.controller,
    this.onPageChanged,
    this.physics,
    this.scrollDirection = Axis.horizontal,
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeInOutCubic,
  }) : super(key: key);

  @override
  AnimatedPageViewState createState() => AnimatedPageViewState();
}

class AnimatedPageViewState extends State<AnimatedPageView>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = widget.controller ?? PageController();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  void animateToPage(int page) {
    _animationController.forward().then((_) {
      _pageController.animateToPage(
        page,
        duration: widget.animationDuration,
        curve: widget.animationCurve,
      );
      _animationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 - (_animation.value * 0.05),
          child: Opacity(
            opacity: 1.0 - (_animation.value * 0.3),
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
                widget.onPageChanged?.call(index);
              },
              physics: widget.physics,
              scrollDirection: widget.scrollDirection,
              children: widget.children,
            ),
          ),
        );
      },
    );
  }
}

Widget pageSlide({
  required Widget content,
  bool isClosing = false,
}) {
  return AnimatedPageSlide(
    content: content,
    isClosing: isClosing,
  );
}

// نسخة بسيطة بدون حركات معقدة لتجنب المشاكل
Widget simplePageSlide({
  required Widget content,
}) {
  return TweenAnimationBuilder<double>(
    duration: const Duration(milliseconds: 400),
    tween: Tween<double>(begin: 0.0, end: 1.0),
    curve: Curves.easeOutCubic,
    builder: (context, value, child) {
      return Transform.translate(
        offset: Offset(0, (1 - value) * 100),
        child: Opacity(
          opacity: value,
          child: Container(
            height: double.infinity,
            width: double.infinity,
            margin: EdgeInsets.only(top: controller.sizedHight * 0.1),
            decoration: BoxDecoration(
              color: AppColors.backgroundColor2.withOpacity(0.989),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(25.5)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                pageSlidingStyle(),
                SizedBox(height: controller.sizedHight * 0.01),
                Expanded(child: content),
              ],
            ),
          ),
        ),
      );
    },
  );
}

// متغير لتتبع حالة الـ bottom sheet
bool _isBottomSheetOpen = false;

// دالة مساعدة لإظهار pageSlide مع حركات جميلة
Future<T?> showAnimatedBottomSheet<T>({
  required BuildContext context,
  required Widget content,
  bool enableDrag = true,
  bool isDismissible = true,
}) async {
  // منع فتح عدة bottom sheets في نفس الوقت
  if (_isBottomSheetOpen) {
    return null;
  }

  _isBottomSheetOpen = true;

  try {
    final result = await showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      isScrollControlled: true,
      builder: (context) => pageSlide(content: content),
    );

    return result;
  } finally {
    _isBottomSheetOpen = false;
  }
}

// دالة بديلة أكثر أماناً لـ showBottomSheet العادي
PersistentBottomSheetController? showSafeBottomSheet({
  required BuildContext context,
  required Widget content,
  bool enableDrag = true,
}) {
  if (_isBottomSheetOpen) {
    return null;
  }

  _isBottomSheetOpen = true;

  final controller = showBottomSheet(
    context: context,
    enableDrag: enableDrag,
    backgroundColor: Colors.transparent,
    builder: (context) => pageSlide(content: content),
  );

  // إعادة تعيين المتغير عند إغلاق الـ sheet
  controller.closed.then((_) {
    _isBottomSheetOpen = false;
  });

  return controller;
}

class AnimatedPageSlide extends StatefulWidget {
  final Widget content;
  final bool isClosing;

  const AnimatedPageSlide({
    Key? key,
    required this.content,
    this.isClosing = false,
  }) : super(key: key);

  @override
  AnimatedPageSlideState createState() => AnimatedPageSlideState();
}

class AnimatedPageSlideState extends State<AnimatedPageSlide>
    with TickerProviderStateMixin {
  AnimationController? _slideController;
  AnimationController? _fadeController;
  AnimationController? _scaleController;

  Animation<Offset>? _slideAnimation;
  Animation<double>? _fadeAnimation;
  Animation<double>? _scaleAnimation;
  Animation<double>? _shadowAnimation;

  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    if (_isDisposed) return;

    // إعداد controllers للحركات
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // إعداد الحركات
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController!,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController!,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController!,
      curve: Curves.elasticOut,
    ));

    _shadowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController!,
      curve: Curves.easeInOut,
    ));

    // بدء الحركات
    _startOpeningAnimations();
  }

  void _startOpeningAnimations() async {
    if (_isDisposed || _slideController == null) return;

    try {
      await Future.delayed(const Duration(milliseconds: 50));
      if (!_isDisposed && _slideController != null && mounted) {
        _slideController!.forward();
        _fadeController!.forward();
      }
      await Future.delayed(const Duration(milliseconds: 100));
      if (!_isDisposed && _scaleController != null && mounted) {
        _scaleController!.forward();
      }
    } catch (e) {
      // تجاهل الأخطاء إذا تم التخلص من الـ widget
    }
  }

  void _startClosingAnimations() async {
    if (_isDisposed || _scaleController == null) return;

    try {
      if (!_isDisposed && _scaleController != null && mounted) {
        _scaleController!.reverse();
      }
      await Future.delayed(const Duration(milliseconds: 100));
      if (!_isDisposed && _fadeController != null && mounted) {
        _fadeController!.reverse();
        _slideController!.reverse();
      }
    } catch (e) {
      // تجاهل الأخطاء إذا تم التخلص من الـ widget
    }
  }

  @override
  void didUpdateWidget(AnimatedPageSlide oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isClosing && !oldWidget.isClosing) {
      _startClosingAnimations();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _slideController?.dispose();
    _fadeController?.dispose();
    _scaleController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isDisposed ||
        _slideController == null ||
        _fadeController == null ||
        _scaleController == null) {
      return Container(); // إرجاع container فارغ إذا تم التخلص من الـ controllers
    }

    return AnimatedBuilder(
      animation: Listenable.merge(
          [_slideController!, _fadeController!, _scaleController!]),
      builder: (context, child) {
        return SlideTransition(
          position:
              _slideAnimation ?? AlwaysStoppedAnimation(const Offset(0, 1)),
          child: FadeTransition(
            opacity: _fadeAnimation ?? AlwaysStoppedAnimation(0.0),
            child: ScaleTransition(
              scale: _scaleAnimation ?? AlwaysStoppedAnimation(0.8),
              child: Container(
                height: double.infinity,
                width: double.infinity,
                margin: EdgeInsets.only(top: controller.sizedHight * 0.1),
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor2
                      .withOpacity(0.989 * (_fadeAnimation?.value ?? 0.0)),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(25.5)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black
                          .withOpacity(0.3 * (_shadowAnimation?.value ?? 0.0)),
                      blurRadius: 25 * (_shadowAnimation?.value ?? 0.0),
                      offset: Offset(0, -8 * (_shadowAnimation?.value ?? 0.0)),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    AnimatedBuilder(
                      animation: _scaleController!,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation?.value ?? 0.8,
                          child: Opacity(
                            opacity: _fadeAnimation?.value ?? 0.0,
                            child: pageSlidingStyle(),
                          ),
                        );
                      },
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.01,
                    ),
                    Expanded(
                      child: AnimatedBuilder(
                        animation: _fadeController!,
                        builder: (context, child) {
                          return Transform.translate(
                            offset: Offset(
                                0, (1 - (_fadeAnimation?.value ?? 0.0)) * 30),
                            child: Opacity(
                              opacity: _fadeAnimation?.value ?? 0.0,
                              child: widget.content,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

Widget pageSetting({
  String? roomN,
  bool connect = true,
  String? privName,
  required String type,
  bool isTv = false,
  bool isSw = false,
  String? id,
  required Function() del,
  required Function() Dfavorite,
  required Function() editRoom,
  required Function(String?) editNames,
  Function(bool?, String?)? editPrivName,
  Function(bool?, String?, int?)? editPrivNameSw,
  Function(String?)? tapOn_star,
}) {
  TextEditingController? editPriv;
  bool privN = false;
  if (isSw) {
    editPriv = TextEditingController(
        text: privName!.split('_')[0] != 'x' ? privName.split('_')[0] : 'X');
  } else {
    editPriv = TextEditingController(
      text: privName != 'x' ? privName : 'X',
    );
  }

  return SingleChildScrollView(
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: controller.sizedWidth * 0.03),
      child: Column(
        children: [
          connect == false
              ? Column(
                  children: [
                    Row(mainAxisSize: MainAxisSize.min, children: [
                      txtStyle(
                        txt: 'غير متصل',
                        color: AppColors.errorColor,
                        align: TextAlign.right,
                      ),
                      Expanded(
                        child: Container(
                          alignment: Alignment.bottomRight,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                  width: controller.sizedWidth * 0.44,
                                  padding: EdgeInsets.only(
                                      right: controller.sized * 0.01),
                                  child: FittedBox(
                                    alignment: Alignment.centerRight,
                                    fit: BoxFit.scaleDown,
                                    child: txtStyle(
                                        align: TextAlign.right,
                                        txt: privName! != 'x'
                                            ? privName
                                            : 'لا يوجد اسم'),
                                  )),
                              Container(
                                  padding: EdgeInsets.only(
                                      left: controller.sizedWidth * 0.01),
                                  decoration: BoxDecoration(
                                      border: Border(
                                          left: BorderSide(
                                              color: AppColors.textColor
                                                  .withOpacity(0.25),
                                              width: 1.5))),
                                  child: txtStyle(
                                    align: TextAlign.right,
                                    txt: type,
                                    color: AppColors.textColor3,
                                  )),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      iconStyle(
                        icon: type == 'مكيف هواء'
                            ? Icons.ac_unit_rounded
                            : type == 'تلفاز'
                                ? Icons.tv_rounded
                                : Icons.power_outlined,
                        color: AppColors.warningColor,
                      ),
                    ]),
                    SizedBox(
                      height: controller.sizedHight * 0.05,
                    ),
                  ],
                )
              : Container(),
          Container(
            width: controller.sizedWidth * 0.85,
            child: TextFormField(
              controller: editPriv,
              maxLength: 16,
              showCursor: true,
              cursorColor: AppColors.primary,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: controller.sized * 0.015,
                fontWeight: FontWeight.w500,
              ),
              onChanged: (i) {
                privN = true;
              },
              onEditingComplete: () {
                FocusManager.instance.primaryFocus?.unfocus();
                if (isSw) {
                  if (editPriv!.text == '' ||
                      editPriv.text == null ||
                      editPriv.text == 'X' ||
                      editPriv.text == 'x') {
                    editPriv.text = privName!.split('_')[0] != 'x'
                        ? privName.split('_')[0]
                        : 'X';
                    privN = false;
                  } else if (editPriv.text != privName) {
                    for (var i = 0; i < editPriv.text.length; i++) {
                      if (arabic.contains(editPriv.text[i]) ||
                          editPriv.text[i].isNumericOnly) {
                        privN = true;
                      } else {
                        editPriv.text = privName!.split('_')[0] != 'x'
                            ? privName.split('_')[0]
                            : 'X';
                        privN = false;
                        break;
                      }
                    }
                    if (privN) {
                      editPrivNameSw!(privN, editPriv.text, 0);
                      privN = false;
                    }
                  }
                } else {
                  if (editPriv!.text == '' ||
                      editPriv.text == null ||
                      editPriv.text == 'X' ||
                      editPriv.text == 'x') {
                    editPriv.text = (privName != 'x' ? privName : 'X')!;
                    privN = false;
                  } else if (editPriv.text != privName) {
                    for (var i = 0; i < editPriv.text.length; i++) {
                      if (arabic.contains(editPriv.text[i]) ||
                          editPriv.text[i].isNumericOnly) {
                        privN = true;
                      } else {
                        editPriv.text = (privName != 'x' ? privName : 'X')!;
                        privN = false;
                        break;
                      }
                    }
                    if (privN) {
                      editPrivName!(privN, editPriv.text);
                      privN = false;
                    }
                  }
                }
              },
              decoration: InputDecoration(
                hintText: 'اسم الملحق الخاص',
                hintStyle: TextStyle(
                  color: AppColors.textHint,
                  fontSize: controller.sized * 0.014,
                  fontWeight: FontWeight.normal,
                ),
                filled: true,
                fillColor: AppColors.surface,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: controller.sizedWidth * 0.04,
                  vertical: controller.sizedHight * 0.015,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.border,
                    width: 1.0,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.border,
                    width: 1.0,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.primary,
                    width: 2.0,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          isTv
              ? Column(
                  children: [
                    containerPageOption(
                      content: Column(
                        children: [
                          MaterialButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              tapOn_star!('show');
                            },
                            child:
                                Row(mainAxisSize: MainAxisSize.min, children: [
                              iconStyle(
                                icon: Icons.menu_open_rounded,
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              txtStyle(
                                align: TextAlign.right,
                                txt: 'اسماء القنوات المفضله',
                              ),
                            ]),
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                IconButton(
                                  onPressed: () {
                                    tapOn_star!('edit');
                                  },
                                  icon: iconStyle(
                                      icon: Icons.edit_outlined,
                                      color: Colors.cyan),
                                ),
                                SizedBox(
                                  width: controller.sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    tapOn_star!('del');
                                  },
                                  icon: iconStyle(
                                      icon: Icons.delete_sweep_rounded,
                                      color: AppColors.errorColor),
                                ),
                                SizedBox(
                                  width: controller.sizedWidth * 0.075,
                                ),
                                IconButton(
                                  onPressed: () {
                                    tapOn_star!('add');
                                  },
                                  icon: iconStyle(
                                      icon: Icons.add_rounded,
                                      color: AppColors.primaryColor),
                                ),
                              ]),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.015,
                    ),
                  ],
                )
              : Container(),
          isSw == false
              ? containerPageOption(
                  content: Column(
                    children: [
                      MaterialButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          editNames('show');
                        },
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                          iconStyle(
                            icon: Icons.menu_open_rounded,
                          ),
                          Expanded(child: SizedBox(width: double.infinity)),
                          txtStyle(
                            align: TextAlign.right,
                            txt: 'اسماء و صفات الجهاز العامة',
                          ),
                        ]),
                      ),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            IconButton(
                              onPressed: () {
                                editNames('edit');
                              },
                              icon: iconStyle(
                                  icon: Icons.edit_outlined,
                                  color: Colors.cyan),
                            ),
                            SizedBox(
                              width: controller.sizedWidth * 0.075,
                            ),
                            IconButton(
                              onPressed: () {
                                editNames('del');
                              },
                              icon: iconStyle(
                                  icon: Icons.delete_sweep_rounded,
                                  color: AppColors.errorColor),
                            ),
                            SizedBox(
                              width: controller.sizedWidth * 0.075,
                            ),
                            IconButton(
                              onPressed: () {
                                editNames('add');
                              },
                              icon: iconStyle(
                                  icon: Icons.add_rounded,
                                  color: AppColors.primaryColor),
                            ),
                          ]),
                    ],
                  ),
                )
              : Container(),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          containerPageOption(
            content: MaterialButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                roomN = editRoom();
                print(roomN);
              },
              child: Row(mainAxisSize: MainAxisSize.min, children: [
                iconStyle(
                  icon: Icons.arrow_drop_down,
                ),
                Expanded(child: SizedBox(width: double.infinity)),
                Row(
                  children: [
                    txtStyle(
                      align: TextAlign.right,
                      txt: '$roomN',
                    ),
                    txtStyle(
                        align: TextAlign.right,
                        txt: 'الغرفة : ',
                        color: AppColors.textColor3),
                  ],
                ),
              ]),
            ),
          ),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          containerPageOption(
            content: MaterialButton(
                padding: EdgeInsets.zero,
                onPressed: Dfavorite,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    iconStyle(
                        icon: Icons.favorite,
                        size: controller.sized * 0.02,
                        color: controller.favorite.contains(id)
                            ? AppColors.errorColor
                            : AppColors.textColor3),
                    Expanded(child: SizedBox(width: double.infinity)),
                    txtStyle(
                        txt: 'الملحقات المفضله', color: AppColors.textColor2),
                  ],
                )),
          ),
          SizedBox(
            height: controller.sizedHight * 0.015,
          ),
          containerPageOption(
            content: MaterialButton(
                padding: EdgeInsets.zero,
                onPressed: del,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    txtStyle(txt: 'حذف الملحق', color: AppColors.errorColor),
                  ],
                )),
          )
        ],
      ),
    ),
  );
}

Widget shortCutStyle(
    {required bool connect,
    required String PrivName,
    required String type,
    required Function() doubleTap,
    required Function() tapOnIcon,
    required List<Widget> content,
    bool? deviceState,
    required Function(bool?) switchState}) {
  return GestureDetector(
    onDoubleTap: doubleTap,
    child: Container(
        margin: EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.055,
            vertical: controller.sizedHight * 0.0075),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(30)),
          color: AppColors.backgroundColor3,
        ),
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: ExpansionTileCard(
              duration: Duration(milliseconds: 400),
              baseColor: Colors.transparent,
              expandedColor: Colors.transparent,
              shadowColor: Colors.transparent,
              borderRadius: BorderRadius.all(Radius.circular(30)),
              contentPadding: EdgeInsets.symmetric(
                  horizontal: controller.sizedWidth * 0.01),
              trailing: connect == false
                  ? Padding(
                      padding: EdgeInsets.all(controller.sized * 0.01),
                      child: txtStyle(
                          txt: 'غير متصل',
                          color: AppColors.errorColor,
                          size: controller.sized * 0.012))
                  : Padding(
                      padding: EdgeInsets.all(controller.sized * 0.0035),
                      child: switchStyle(
                          value: deviceState!, onChanged: switchState),
                    ),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  IconButton(
                    onPressed: tapOnIcon,
                    icon: iconStyle(
                        icon: type == 'مكيف'
                            ? Icons.ac_unit_rounded
                            : type == 'تلفاز'
                                ? Icons.tv_rounded
                                : Icons.power_outlined,
                        color: AppColors.warningColor),
                  ),
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.zero,
                      // padding:
                      //     EdgeInsets.only(right: controller.sizedWidth * 0.01),
                      // color: Colors.blueGrey.shade600,
                      // alignment: Alignment.bottomRight,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Container(
                            padding: EdgeInsets.only(
                              left: controller.sizedWidth * 0.01,
                            ),
                            decoration: BoxDecoration(
                                border: Border(
                                    left: BorderSide(
                                        color: AppColors.textColor3
                                            .withOpacity(0.45),
                                        width: 1))),
                            child: txtStyle(
                              align: TextAlign.right,
                              txt: type,
                            ),
                          ),
                          Container(
                              width: controller.sizedWidth * 0.32,
                              padding: EdgeInsets.only(
                                  right: controller.sizedWidth * 0.01),
                              child: txtStyle(
                                align: TextAlign.right,
                                txt: PrivName == 'x'
                                    ? 'لا يوجد اسم'
                                    : PrivName.length > 9
                                        ? PrivName.substring(0, 9) + '...'
                                        : PrivName,
                              )),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              children: connect == true ? content : []),
        )),
  );
}

Widget pageSlidingStyle() => FractionallySizedBox(
      widthFactor: controller.sizedWidth * 0.0008,
      child: Container(
        margin: EdgeInsets.symmetric(
          vertical: controller.sizedHight * 0.025,
        ),
        child: Container(
          height: controller.sizedHight * 0.007,
          decoration: BoxDecoration(
            color: AppColors.containerPageColor.withOpacity(0.99),
            borderRadius: const BorderRadius.all(Radius.circular(2.5)),
          ),
        ),
      ),
    );

Widget containerPageOption({
  required Widget content,
  double ver = 0,
}) {
  return GetBuilder<SettingsController>(
    builder: (settingsController) => Container(
      padding: EdgeInsets.symmetric(
          horizontal: controller.sizedWidth * 0.03,
          vertical: controller.sizedHight * ver),
      width: controller.sizedWidth * 0.85,
      // height: controller.sizedHight * 0.4,
      decoration: BoxDecoration(
        color: AppColors.containerPageColor,
        borderRadius: const BorderRadius.all(Radius.circular(17)),
      ),
      child: content,
    ),
  );
}

Widget containerIconsOption({
  required Widget content,
  double ver = 0,
  double radius = 35,
  Color? color,
  EdgeInsets margin = EdgeInsets.zero,
  EdgeInsets padding = EdgeInsets.zero,
}) {
  return Container(
    padding: padding == EdgeInsets.zero
        ? EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.01,
            vertical: controller.sizedHight * ver)
        : padding,
    margin: margin,
    // width: controller.sizedWidth * 0.85,
    // height: controller.sizedHight * 0.4,
    decoration: BoxDecoration(
      gradient: color == null ? AppColors.cardGradient : null,
      color: color,
      borderRadius: BorderRadius.all(Radius.circular(radius)),
      border: Border.all(
        color: AppColors.border,
        width: 1.0,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.04),
          spreadRadius: 1,
          blurRadius: 8,
          offset: Offset(0, 2),
        ),
      ],
    ),
    child: content,
  );
}

// === ويدجات الحقول النصية المخصصة ===

/// ويدجت مخصص لـ TextFormField يتناسب مع الوضعين الداكن والفاتح
///
/// مثال على الاستخدام:
/// ```dart
/// customTextFormField(
///   textController: myController,
///   hintText: 'أدخل النص هنا',
///   maxLength: 50,
///   onChanged: (value) => print(value),
/// )
/// ```
Widget customTextFormField({
  required TextEditingController textController,
  String? hintText,
  String? labelText,
  int? maxLength,
  bool obscureText = false,
  TextInputType? keyboardType,
  Function(String)? onChanged,
  Function()? onEditingComplete,
  Function(String)? onFieldSubmitted,
  String? Function(String?)? validator,
  bool enabled = true,
  TextDirection textDirection = TextDirection.rtl,
  TextAlign textAlign = TextAlign.right,
  int maxLines = 1,
  Widget? suffixIcon,
  Widget? prefixIcon,
}) {
  return TextFormField(
    controller: textController,
    maxLength: maxLength,
    obscureText: obscureText,
    keyboardType: keyboardType,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
    onFieldSubmitted: onFieldSubmitted,
    validator: validator,
    enabled: enabled,
    textDirection: textDirection,
    textAlign: textAlign,
    maxLines: maxLines,
    showCursor: true,
    cursorColor: AppColors.primary,
    style: TextStyle(
      color: AppColors.textPrimary,
      fontSize: controller.sized * 0.015,
      fontWeight: FontWeight.w500,
    ),
    decoration: InputDecoration(
      hintText: hintText,
      labelText: labelText,
      hintStyle: TextStyle(
        color: AppColors.textHint,
        fontSize: controller.sized * 0.014,
        fontWeight: FontWeight.normal,
      ),
      labelStyle: TextStyle(
        color: AppColors.textSecondary,
        fontSize: controller.sized * 0.014,
        fontWeight: FontWeight.w500,
      ),
      filled: true,
      fillColor: AppColors.surface,
      contentPadding: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.04,
        vertical: controller.sizedHight * 0.015,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.error,
          width: 1.5,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.error,
          width: 2.0,
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.textDisabled,
          width: 1.0,
        ),
      ),
      suffixIcon: suffixIcon,
      prefixIcon: prefixIcon,
    ),
  );
}

/// ويدجت مخصص لـ TextField يتناسب مع الوضعين الداكن والفاتح
Widget customTextField({
  required TextEditingController textController,
  String? hintText,
  bool obscureText = false,
  TextInputType? keyboardType,
  Function(String)? onChanged,
  Function()? onEditingComplete,
  Function(String)? onSubmitted,
  bool enabled = true,
  TextDirection textDirection = TextDirection.rtl,
  TextAlign textAlign = TextAlign.right,
  int maxLines = 1,
  int? maxLength,
  Widget? suffixIcon,
  Widget? prefixIcon,
}) {
  return TextField(
    controller: textController,
    obscureText: obscureText,
    keyboardType: keyboardType,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
    onSubmitted: onSubmitted,
    enabled: enabled,
    textDirection: textDirection,
    textAlign: textAlign,
    maxLines: maxLines,
    maxLength: maxLength,
    showCursor: true,
    cursorColor: AppColors.primary,
    style: TextStyle(
      color: AppColors.textPrimary,
      fontSize: controller.sized * 0.015,
      fontWeight: FontWeight.w500,
    ),
    decoration: InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(
        color: AppColors.textHint,
        fontSize: controller.sized * 0.014,
        fontWeight: FontWeight.normal,
      ),
      filled: true,
      fillColor: AppColors.surface,
      contentPadding: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.04,
        vertical: controller.sizedHight * 0.015,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2.0,
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: AppColors.textDisabled,
          width: 1.0,
        ),
      ),
      suffixIcon: suffixIcon,
      prefixIcon: prefixIcon,
    ),
  );
}

/// ويدجت مبسط لحقل النص مع التنسيق الأساسي
Widget simpleTextFormField({
  required TextEditingController textController,
  String? hintText,
  int? maxLength,
  Function(String)? onChanged,
  Function()? onEditingComplete,
}) {
  return customTextFormField(
    textController: textController,
    hintText: hintText,
    maxLength: maxLength,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
  );
}

/// ويدجت مبسط لحقل كلمة المرور
Widget passwordTextFormField({
  required TextEditingController textController,
  String? hintText,
  bool obscureText = true,
  Function(String)? onChanged,
  Function()? onEditingComplete,
  Widget? suffixIcon,
}) {
  return customTextFormField(
    textController: textController,
    hintText: hintText ?? 'كلمة المرور',
    obscureText: obscureText,
    onChanged: onChanged,
    onEditingComplete: onEditingComplete,
    suffixIcon: suffixIcon,
  );
}

// === مثال على الاستخدام ===
/*
// بدلاً من:
TextFormField(
  controller: editPriv,
  maxLength: 16,
  cursorColor: AppColors.textColor.withOpacity(0.3),
  textDirection: TextDirection.rtl,
  style: TextStyle(color: AppColors.textColor.withOpacity(0.6)),
  onChanged: (i) { privN = true; },
  decoration: InputDecoration(...),
)

// استخدم:
customTextFormField(
  textController: editPriv,
  hintText: 'اسم الغرفة',
  maxLength: 16,
  onChanged: (i) { privN = true; },
  onEditingComplete: () {
    // منطق التحقق هنا
  },
)

// أو للاستخدام السريع:
simpleTextFormField(
  textController: editPriv,
  hintText: 'اسم الغرفة',
  maxLength: 16,
  onChanged: (i) { privN = true; },
)
*/

Widget submitButtom({String text = "موافق", required Function() onPressed}) {
  return OutlinedButton(
      child: Text(
        text,
        textDirection: TextDirection.rtl,
        style: TextStyle(
            color: AppColors.textColor,
            fontSize: controller.sized * 0.02,
            fontWeight: FontWeight.bold),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide.none,
        backgroundColor: AppColors.primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(25),
          ),
        ),
      ),
      onPressed: onPressed);
}

Widget delButtom({String text = "حذف", required Function() onPressed}) {
  return OutlinedButton(
      child: Text(
        text,
        textDirection: TextDirection.rtl,
        style: TextStyle(
            color: AppColors.white,
            fontSize: controller.sized * 0.02,
            fontWeight: FontWeight.bold),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide.none,
        backgroundColor: AppColors.error,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(25),
          ),
        ),
      ),
      onPressed: onPressed);
}

Widget switchStyle(
    {required Function(bool?) onChanged,
    required bool value,
    double size = 0}) {
  if (size == 0) {
    size = controller.sized * 0.0008;
  }
  return Transform.scale(
    scale: size,
    child: Switch(
        activeColor: AppColors.success,
        inactiveThumbColor: AppColors.backgroundColor3,
        activeTrackColor: AppColors.success.withOpacity(0.2),
        inactiveTrackColor: AppColors.textColor2.withOpacity(0.8),
        trackOutlineWidth: WidgetStateProperty.resolveWith((states) {
          return 0.5; // Ширина контура при выключенном состоянии
        }),
        trackOutlineColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.success
                .withOpacity(0.5); // Цвет контура при включенном состоянии
          }
          return AppColors.textSecondary
              .withOpacity(0.2); // Цвет контура при выключенном состоянии
        }),
        value: value,
        onChanged: onChanged),
  );
}

/// مفتاح مخصص مع أيقونة داخلية للثيم - نفس تصميم المفتاح العادي
Widget themeSwitchStyle({
  required Function(bool?) onChanged,
  required bool value,
  double size = 0,
}) {
  if (size == 0) {
    size = controller.sized * 0.001;
  }

  return Transform.scale(
    scale: size,
    child: GestureDetector(
      onTap: () => onChanged(!value),
      child: Container(
        width: 51, // نفس عرض المفتاح العادي
        height: 31, // نفس ارتفاع المفتاح العادي
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.5),
          gradient: LinearGradient(
            colors: value
                ? [
                    const Color(0xFF1A1A2E), // بنفسجي ليلي داكن
                    const Color(0xFF16213E), // أزرق ليلي عميق
                  ]
                : [
                    const Color(0xFFFF8F00), // برتقالي ذهبي
                    const Color(0xFFFFB300), // برتقالي فاتح
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: value
                  ? const Color(0xFF1A1A2E).withOpacity(0.6)
                  : const Color(0xFFFF8F00).withOpacity(0.4),
              blurRadius: 8,
              offset: const Offset(0, 3),
              spreadRadius: 0,
            ),
          ],
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          alignment: value ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.all(2),
            width: 27,
            height: 27,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.white, // أبيض في كلا الوضعين
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 150),
              child: Icon(
                value ? Icons.nightlight_round : Icons.wb_sunny_rounded,
                key: ValueKey(value),
                size: 16,
                color: value
                    ? const Color(0xFF1A1A2E) // بنفسجي ليلي داكن للقمر
                    : const Color(0xFFFF8F00), // برتقالي ذهبي للشمس
              ),
            ),
          ),
        ),
      ),
    ),
  );
}

Widget txtStyle(
    {required String txt,
    Color? color,
    double size = 0,
    int maxLines = 1,
    TextAlign align = TextAlign.center,
    TextDirection txtDirection = TextDirection.rtl}) {
  if (color == null) {
    color = AppColors.textSecondary;
  }
  if (size == 0) {
    size = controller.sized * 0.0125;
  }
  return Text(
    txt,
    textAlign: align,
    maxLines: maxLines,
    textDirection: txtDirection,
    style: TextStyle(
        height: controller.sizedHight * 0.00125,
        color: color,
        fontSize: size,
        fontWeight: FontWeight.bold),
  );
}

Widget iconStyle({required IconData icon, Color? color, double size = 0}) {
  if (color == null) {
    color = AppColors.textSecondary;
  }
  if (size == 0) {
    size = controller.sized * 0.03;
  }

  return Icon(
    icon,
    color: color,
    size: size,
  );
}
