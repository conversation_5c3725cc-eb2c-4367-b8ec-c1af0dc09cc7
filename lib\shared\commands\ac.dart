import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

commandAc(bool command, Map ac, String roomId) {
  if (client.connectionStatus!.state.name == 'connected') {
    print('يرسل الامر الى السيرفر ');
    // getRoomDevices();
    switchTap('state', ac['state'], ac['id']);
    roomState = false;
    for (var j in controller.rooms[roomId]['devices'].values) {
      if (j['state'] == true) {
        roomState = true;
      }
      // setState(() {

      // });
    }
    controller.rooms[roomId]['state'] = roomState;
    if (command == true) {
      controller.homeState = true;
    } else {
      controller.homeState = false;
      for (var i in controller.rooms.values) {
        if (ac['state'] == true) {
          controller.homeState = true;
        }
      }
    }
    final builder = MqttClientPayloadBuilder();

    if (command == true) {
      // تحديد درجة الحرارة حسب نوع التشغيل
      String temperature = ac['type'].toString() == 'مروحه'
          ? 'X'
          : (ac['degree'] != null ? ac['degree'].toInt().toString() : 'X');

      // تحديد سرعة المروحة
      String fanSpeed = ac['speed'] != null ? ac['speed'].toString() : '0';

      // تحديد نوع التشغيل
      String acType;
      switch (ac['type'].toString()) {
        case 'تبريد':
          acType = 'AC';
          break;
        case 'تدفئه':
          acType = 'HEAT';
          break;
        case 'مروحه':
          acType = 'VAN';
          break;
        default:
          acType = 'XX';
      }

      builder
          .addString('${ac['id']} AC RUN $temperature VAN $fanSpeed $acType');
    } else {
      builder.addString('${ac['id']} AC OFF');
    }
    client.publishMessage(
        '${controller.homeId}/app/zain', MqttQos.atLeastOnce, builder.payload!);

    controller.update();
  }
}
