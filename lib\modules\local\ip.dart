import 'dart:io';
import 'package:get/get.dart';
import 'package:multicast_dns/multicast_dns.dart';
import 'package:zaen/controller/controller.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:zaen/shared/themes/app_colors.dart';

HomeController controller = Get.find();

findIp() async {
  try {
    String name = '_workstation._tcp';
    final info = NetworkInfo();

    final wifiName = await info.getWifiName(); // "FooNetwork"
    final wifiBSSID = await info.getWifiBSSID(); // 11:22:33:44:55:66
    final wifiIP = await info.getWifiGatewayIP(); // ************

    // print(wifiBSSID);
    // print(controller.systems);
    // print(wifiName!);

    if (controller.reg == false &&
        wifiName != null &&
        wifiName.toUpperCase().contains(wifiBSSID!.toUpperCase())) {
      controller.wifi = wifiName;
      controller.reg = true;
      controller.apMAC = wifiBSSID;
      controller.apName = wifiName.replaceAll('"', '').split('/')[0];
      Get.toNamed('reg');
    } else if (wifiName != null) {
      String hostZain = '';
      controller.hostZain.value = '';
      controller.wifi = wifiName;
      controller.reg = false;
      // Get.toNamed('home');

      // فحص الاتصال بالشبكة المحلية أولاً
      bool networkAvailable = false;
      try {
        // فحص إذا كان هناك gateway IP (يعني متصل بشبكة محلية)
        if (wifiIP != null && wifiIP.isNotEmpty) {
          networkAvailable = true;
        } else {
          // محاولة فحص الشبكة المحلية
          final socket =
              await RawDatagramSocket.bind(InternetAddress.anyIPv4, 0);
          socket.close();
          networkAvailable = true;
        }
      } catch (e) {
        print('Network check failed: $e');
        networkAvailable = false;
      }

      if (!networkAvailable) {
        print('No local network connection available, skipping mDNS lookup');
        return;
      }

      MDnsClient? client;
      try {
        client = MDnsClient(rawDatagramSocketFactory: (dynamic host, int port,
            {bool? reuseAddress, bool? reusePort, int? ttl}) {
          return RawDatagramSocket.bind(host, port,
              reuseAddress: true, reusePort: false, ttl: ttl!);
        });

        await client.start();

        // إضافة timeout قصير للبحث
        await for (final PtrResourceRecord ptr in client
            .lookup<PtrResourceRecord>(ResourceRecordQuery.serverPointer(name))
            .timeout(Duration(seconds: 3))) {
          print('PTR: ${ptr.domainName.toString()}');
          if (ptr.domainName.toString().contains('zain')) {
            print('yes');

            await for (final SrvResourceRecord srv in client
                .lookup<SrvResourceRecord>(
                    ResourceRecordQuery.service(ptr.domainName))
                .timeout(Duration(seconds: 2))) {
              // print('SRV target: ${srv.target} port: ${srv.port}');

              await for (final IPAddressResourceRecord ip in client
                  .lookup<IPAddressResourceRecord>(
                      ResourceRecordQuery.addressIPv4(srv.target))
                  .timeout(Duration(seconds: 2))) {
                print('IP: ${ip.address.toString()}');
                hostZain = ip.address.host;
                controller.hostZain.value = hostZain;
                // controller.update();
                print(controller.hostZain.value);

                break;
              }
              if (hostZain.isNotEmpty) {
                break;
              }
            }
            if (hostZain.isNotEmpty) {
              break;
            }
          }
          if (hostZain.isNotEmpty) {
            break;
          }
        }
      } catch (e) {
        print('Error during mDNS lookup: $e');
        // في حالة فشل mDNS، محاولة استخدام IPs افتراضية شائعة
        await _tryCommonIPs();
      } finally {
        try {
          client?.stop();
        } catch (e) {
          print('Error stopping mDNS client: $e');
        }
      }

      print(hostZain);
      print('done findIp');
    }
  } catch (e) {
    print('Error in findIp: $e');
    // معالجة الخطأ العام
  }
}

// دالة لمحاولة IPs افتراضية شائعة
Future<void> _tryCommonIPs() async {
  List<String> commonIPs = [
    '*************',
    '*************',
    '*************',
    '*************',
    '*************',
    '*************',
    '**********',
    '**********',
  ];

  for (String ip in commonIPs) {
    try {
      // محاولة الاتصال بالIP للتحقق من وجود الخادم
      final socket =
          await Socket.connect(ip, 3306, timeout: Duration(seconds: 1));
      socket.destroy();

      // إذا نجح الاتصال، استخدم هذا IP
      controller.hostZain.value = ip;
      print('Found server at: $ip');
      break;
    } catch (e) {
      // تجاهل الخطأ والمحاولة مع IP التالي
      continue;
    }
  }
}
