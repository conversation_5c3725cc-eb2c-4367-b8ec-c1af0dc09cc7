-- Merging decision tree log ---
application
INJECTED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:12:5-43:19
INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f6331ab98f9705211be04a87a5f2d9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f6331ab98f9705211be04a87a5f2d9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:1:1-55:12
MERGED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:1:1-55:12
INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml:1:1-8:12
INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml:1:1-8:12
INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml:1:1-8:12
MERGED from [:flutter_keyboard_visibility] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_keyboard_visibility-6.0.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.23\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:network_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.0.13\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:rive_common] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\rive_common-0.4.12\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.3.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\774cb9cecc43277739dbfeb362c4b718\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-3\b588a97f580bfb3e505af5bac86436f3\transformed\jetified-activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee4c1cfe0cd23a57d7eacd79a18400f0\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fdb931e7aced4c4361c2a1a388a711d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8415eee5356c0c2f86e597a8fde597d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\72772b42aa2a00700ae9154a92d013b4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2488d02a42348edc764f80a031727b4e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e375bd9b15c7863c0ffd956e3d89824\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\21f5521d47a3985f17891551678788af\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ef0412457dc84f486400cfaad4953af\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\bde92eedf717fc299f31d6b0932e45af\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8ced008bc55173228513941f754c3de\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\51eac60701b2a6ea45c32cdce7169426\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cba2c84661622d304027c2174af8b25\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c363a6e185b0bb1ac4e21f23756661da\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9d7fd95de23e0243db8260125935a3f\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\0a61b5aa7497d15bc35c598b6f720b65\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\497b85a97ee869e66cbf49721ce584f1\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed7fc341d8167334f39f0e2aace364ec\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f6331ab98f9705211be04a87a5f2d9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4e9b51431a8dbaf4218f5f1add92c43\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ecefa2f51bd04cfad5a026cff22bfe9\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\46e5d1e806237487d33a7ea5f9ea6e87\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:2:5-31
		INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:6:5-75
MERGED from [:network_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-76
MERGED from [:network_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-76
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-76
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-76
	android:name
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:6:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:7:5-75
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-76
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-76
	android:name
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:8:5-78
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-79
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-79
	android:name
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:9:5-85
	android:name
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:9:22-82
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:10:5-79
MERGED from [:network_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [:network_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:10:22-76
queries
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:49:5-54:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:50:9-53:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:51:13-72
	android:name
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:51:21-70
data
ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:52:13-50
	android:mimeType
		ADDED from D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:52:19-48
uses-sdk
INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_keyboard_visibility] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_keyboard_visibility-6.0.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_keyboard_visibility] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_keyboard_visibility-6.0.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.23\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.23\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:network_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:network_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.0.13\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.0.13\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\rive_common-0.4.12\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\rive_common-0.4.12\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.3.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.3.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\774cb9cecc43277739dbfeb362c4b718\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\774cb9cecc43277739dbfeb362c4b718\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-3\b588a97f580bfb3e505af5bac86436f3\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-3\b588a97f580bfb3e505af5bac86436f3\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee4c1cfe0cd23a57d7eacd79a18400f0\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee4c1cfe0cd23a57d7eacd79a18400f0\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fdb931e7aced4c4361c2a1a388a711d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fdb931e7aced4c4361c2a1a388a711d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8415eee5356c0c2f86e597a8fde597d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8415eee5356c0c2f86e597a8fde597d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\72772b42aa2a00700ae9154a92d013b4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\72772b42aa2a00700ae9154a92d013b4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2488d02a42348edc764f80a031727b4e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2488d02a42348edc764f80a031727b4e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e375bd9b15c7863c0ffd956e3d89824\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e375bd9b15c7863c0ffd956e3d89824\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\21f5521d47a3985f17891551678788af\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\21f5521d47a3985f17891551678788af\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ef0412457dc84f486400cfaad4953af\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ef0412457dc84f486400cfaad4953af\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\bde92eedf717fc299f31d6b0932e45af\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\bde92eedf717fc299f31d6b0932e45af\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8ced008bc55173228513941f754c3de\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8ced008bc55173228513941f754c3de\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\51eac60701b2a6ea45c32cdce7169426\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\51eac60701b2a6ea45c32cdce7169426\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cba2c84661622d304027c2174af8b25\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5cba2c84661622d304027c2174af8b25\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c363a6e185b0bb1ac4e21f23756661da\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c363a6e185b0bb1ac4e21f23756661da\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9d7fd95de23e0243db8260125935a3f\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9d7fd95de23e0243db8260125935a3f\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\0a61b5aa7497d15bc35c598b6f720b65\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\0a61b5aa7497d15bc35c598b6f720b65\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\497b85a97ee869e66cbf49721ce584f1\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\497b85a97ee869e66cbf49721ce584f1\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed7fc341d8167334f39f0e2aace364ec\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed7fc341d8167334f39f0e2aace364ec\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f6331ab98f9705211be04a87a5f2d9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f6331ab98f9705211be04a87a5f2d9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4e9b51431a8dbaf4218f5f1add92c43\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4e9b51431a8dbaf4218f5f1add92c43\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ecefa2f51bd04cfad5a026cff22bfe9\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ecefa2f51bd04cfad5a026cff22bfe9\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\46e5d1e806237487d33a7ea5f9ea6e87\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\46e5d1e806237487d33a7ea5f9ea6e87\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\flutter training\zaen\android\app\src\debug\AndroidManifest.xml
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-81
	android:name
		ADDED from [:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.zaen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.zaen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
