1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.zaen"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:5:5-67
15-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:5:22-64
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:6:5-75
16-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:6:22-73
17    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
17-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:7:5-75
17-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:7:22-73
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:8:5-78
18-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:8:22-76
19    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
19-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:9:5-85
19-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:9:22-82
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:10:5-79
20-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:10:22-76
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:49:5-54:15
29        <intent>
29-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:50:9-53:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:51:13-72
30-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:51:21-70
31
32            <data android:mimeType="text/plain" />
32-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:52:13-50
32-->D:\flutter training\zaen\android\app\src\main\AndroidManifest.xml:52:19-48
33        </intent>
34    </queries> <!-- required for: `WifiManager.getScanResult`, `WifiManager.startScan` -->
35    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
35-->[:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-81
35-->[:wifi_scan] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-78
36
37    <permission
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.zaen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.zaen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
42
43    <application
44        android:name="android.app.Application"
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:extractNativeLibs="true"
48        android:icon="@mipmap/ic_launcher"
49        android:label="zaen" >
50        <activity
51            android:name="com.example.zaen.MainActivity"
52            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
53            android:exported="true"
54            android:hardwareAccelerated="true"
55            android:launchMode="singleTop"
56            android:taskAffinity=""
57            android:theme="@style/LaunchTheme"
58            android:windowSoftInputMode="adjustResize" >
59
60            <!--
61                 Specifies an Android theme to apply to this Activity as soon as
62                 the Android process has started. This theme is visible to the user
63                 while the Flutter UI initializes. After that, this theme continues
64                 to determine the Window background behind the Flutter UI.
65            -->
66            <meta-data
67                android:name="io.flutter.embedding.android.NormalTheme"
68                android:resource="@style/NormalTheme" />
69
70            <intent-filter>
71                <action android:name="android.intent.action.MAIN" />
72
73                <category android:name="android.intent.category.LAUNCHER" />
74            </intent-filter>
75        </activity>
76        <!--
77             Don't delete the meta-data below.
78             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
79        -->
80        <meta-data
81            android:name="flutterEmbedding"
82            android:value="2" />
83
84        <provider
84-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
85            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
85-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
86            android:authorities="com.example.zaen.flutter.image_provider"
86-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
87            android:exported="false"
87-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
88            android:grantUriPermissions="true" >
88-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
89            <meta-data
89-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
90                android:name="android.support.FILE_PROVIDER_PATHS"
90-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
91                android:resource="@xml/flutter_image_picker_file_paths" />
91-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
92        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
93        <service
93-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
94            android:name="com.google.android.gms.metadata.ModuleDependencies"
94-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
95            android:enabled="false"
95-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
96            android:exported="false" >
96-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
97            <intent-filter>
97-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
98                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
98-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
98-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
99            </intent-filter>
100
101            <meta-data
101-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
102                android:name="photopicker_activity:0:required"
102-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
103                android:value="" />
103-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
104        </service>
105
106        <uses-library
106-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
107            android:name="androidx.window.extensions"
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
108            android:required="false" />
108-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
109        <uses-library
109-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
110            android:name="androidx.window.sidecar"
110-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
111            android:required="false" />
111-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
112
113        <provider
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
114            android:name="androidx.startup.InitializationProvider"
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
115            android:authorities="com.example.zaen.androidx-startup"
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
116            android:exported="false" >
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
117            <meta-data
117-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
119                android:value="androidx.startup" />
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
120            <meta-data
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
121                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
122                android:value="androidx.startup" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
123        </provider>
124
125        <receiver
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
126            android:name="androidx.profileinstaller.ProfileInstallReceiver"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
127            android:directBootAware="false"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
128            android:enabled="true"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
129            android:exported="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
130            android:permission="android.permission.DUMP" >
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
132                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
133            </intent-filter>
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
135                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
138                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
141                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
142            </intent-filter>
143        </receiver>
144    </application>
145
146</manifest>
