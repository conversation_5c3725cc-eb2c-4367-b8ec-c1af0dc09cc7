import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/components.dart';

class ThemeSettingsPage extends StatelessWidget {
  const ThemeSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsController = Get.find<SettingsController>();
    
    return Scaffold(
      appBar: AppBar(
        title: txtStyle(
          txt: 'إعدادات المظهر',
          color: AppColors.textColor,
        ),
        backgroundColor: AppColors.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.textColor,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      backgroundColor: AppColors.backgroundColor,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            txtStyle(
              txt: 'اختر مظهر التطبيق',
              color: AppColors.textColor,
              size: 18,
            ),
            const SizedBox(height: 8),
            txtStyle(
              txt: 'يمكنك اختيار الوضع الداكن أو الفاتح حسب تفضيلك',
              color: AppColors.secondaryTextColor,
              size: 14,
            ),
            const SizedBox(height: 24),
            
            // بطاقة إعدادات الثيم
            Container(
              decoration: BoxDecoration(
                color: AppColors.surfaceColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.current.shadow,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // خيار الوضع الفاتح
                  Obx(() => _buildThemeOption(
                    context: context,
                    title: 'الوضع الفاتح',
                    subtitle: 'مظهر فاتح ومريح للعين',
                    icon: Icons.light_mode,
                    isSelected: !settingsController.isDarkMode.value,
                    onTap: () => settingsController.setDarkMode(false),
                  )),
                  
                  Divider(
                    color: AppColors.current.outline.withOpacity(0.3),
                    height: 1,
                  ),
                  
                  // خيار الوضع الداكن
                  Obx(() => _buildThemeOption(
                    context: context,
                    title: 'الوضع الداكن',
                    subtitle: 'مظهر داكن يوفر الطاقة ومريح في الإضاءة المنخفضة',
                    icon: Icons.dark_mode,
                    isSelected: settingsController.isDarkMode.value,
                    onTap: () => settingsController.setDarkMode(true),
                  )),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // معلومات إضافية
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.current.secondary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.current.secondary.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.current.secondary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        txtStyle(
                          txt: 'نصيحة',
                          color: AppColors.current.secondary,
                          size: 16,
                        ),
                        const SizedBox(height: 4),
                        txtStyle(
                          txt: 'سيتم حفظ اختيارك تلقائياً وتطبيقه في المرة القادمة التي تفتح فيها التطبيق',
                          color: AppColors.secondaryTextColor,
                          size: 12,
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const Spacer(),
            
            // معلومات الحالة الحالية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.current.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.palette,
                    color: AppColors.current.primary,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Obx(() => txtStyle(
                    txt: 'المظهر الحالي: ${settingsController.currentThemeName}',
                    color: AppColors.current.primary,
                    size: 16,
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildThemeOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected 
            ? AppColors.current.primary.withOpacity(0.2)
            : AppColors.current.outline.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isSelected 
            ? AppColors.current.primary
            : AppColors.secondaryTextColor,
          size: 24,
        ),
      ),
      title: txtStyle(
        txt: title,
        color: AppColors.textColor,
        size: 16,
        align: TextAlign.right,
      ),
      subtitle: txtStyle(
        txt: subtitle,
        color: AppColors.secondaryTextColor,
        size: 12,
        maxLines: 2,
        align: TextAlign.right,
      ),
      trailing: isSelected
        ? Icon(
            Icons.check_circle,
            color: AppColors.current.primary,
            size: 24,
          )
        : Icon(
            Icons.radio_button_unchecked,
            color: AppColors.secondaryTextColor,
            size: 24,
          ),
      onTap: onTap,
    );
  }
}
