import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';

editRoomNames({context, roomId, string}) async {
  if (client.connectionStatus!.state.name == 'connected') {
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));
    Results getNames = await conn.query('select ${roomId} from Rooms');
    conn.close();
    List names = [];

    for (var n in getNames) {
      if (n.fields.values.toList()[0] != null) {
        names.add(n.fields.values.toList()[0]);
      }
    }
    if (names.length == 0) {
      string = 'add';
    }
    TextEditingController name = TextEditingController(
      text: string == 'edit' ? names[0] : '',
    );
    bool edit = false;

    print(names);
    int selectname = 0;

    AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.backgroundColor3,
      btnOkText: string == 'add'
          ? 'اضافة'
          : string == 'del'
              ? 'حذف'
              : string == 'edit'
                  ? 'تعديل'
                  : 'موافق',

      body: Column(
        children: [
          SizedBox(
            height: controller.sizedHight * 0.25,
            child: CupertinoPicker(
              squeeze: 1.2,
              // looping: true,
              useMagnifier: true,
              magnification: 1.25,
              // scrollController: FixedExtentScrollController(initialItem: selectitem),
              itemExtent: controller.sizedHight * 0.05, //height of each item

              backgroundColor: Colors.transparent,
              children: <Widget>[
                for (var c in names)
                  if (c != null)
                    Center(
                      child: Text(
                        c.toString(),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: AppColors.textPrimary.withOpacity(0.8),
                            fontSize: controller.sized * 0.015,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
              ],
              onSelectedItemChanged: (int index) {
                selectname = index;
                if (string == 'edit') {
                  name.text = names[selectname];
                }
              },
            ),
          ),
          string == 'edit' || string == 'add'
              ? Material(
                  color: Colors.transparent,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      children: [
                        TextFormField(
                          controller: name,
                          maxLength: 25,
                          autofocus: true,
                          showCursor: true,
                          cursorColor: AppColors.primary,
                          textDirection: TextDirection.rtl,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: controller.sized * 0.015,
                            fontWeight: FontWeight.w500,
                          ),
                          onEditingComplete: () {
                            FocusManager.instance.primaryFocus?.unfocus();
                          },
                          decoration: InputDecoration(
                            hintText: 'اسم العنصر',
                            hintStyle: TextStyle(
                              color: AppColors.textHint,
                              fontSize: controller.sized * 0.014,
                              fontWeight: FontWeight.normal,
                            ),
                            filled: true,
                            fillColor: AppColors.surface,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.04,
                              vertical: controller.sizedHight * 0.015,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.border,
                                width: 1.0,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.border,
                                width: 1.0,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.primary,
                                width: 2.0,
                              ),
                            ),
                            suffixIcon: Icon(
                              Icons.edit_rounded,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Container()
        ],
      ),

      btnOkOnPress: () async {
        FocusManager.instance.primaryFocus?.unfocus();
        if (name.text == '' || name.text == null) {
          name.text = string == 'edit' ? names[0] : '';
          edit = false;
        } else if (names.contains(name.text) == false) {
          for (var i = 0; i < name.text.length; i++) {
            if (arabic.contains(name.text[i]) || name.text[i].isNumericOnly) {
              edit = true;
            } else {
              name.text = string == 'edit' ? names[0] : '';
              break;
            }
          }
        }
        if (string != 'show') {
          final conn = await MySqlConnection.connect(ConnectionSettings(
              host: controller.hostZain.value,
              // port: 80,
              user: 'root',
              db: 'zain',
              password: 'zain',
              characterSet: CharacterSet.UTF8));
          if (string == 'edit' && edit) {
            await conn.query('update Rooms set ${roomId}=? where ${roomId}=?',
                [name.text.toString(), names[selectname]]);
            getDevices();
          } else if (string == 'add' && edit) {
            print('1111111111114444444442222222222222');
            var newName = await conn.query(
                'update Rooms set ${roomId}=? where ${roomId} IS NULL LIMIT 1',
                [
                  name.text.toString(),
                ]);
            if (newName.affectedRows! > 0) {
              print('تم تعديل البيانات بنجاح.');
            } else {
              print('لم يتم تنفيذ أي تعديل . سيتم اضافة سطر جديد');
              await conn.query('insert INTO Rooms(${roomId}) values(?)', [
                name.text.toString(),
              ]);
            }

            getDevices();
          } else if (string == 'del') {
            print(names[selectname]);
            if (names.length != 1) {
              await conn.query(
                  'UPDATE Rooms SET ${roomId} = NULL WHERE ${roomId} = ?', [
                names[selectname],
              ]);
              getDevices();
            }
          }

          conn.close();
        }
      },
      // btnCancelOnPress:
      //     () {},
    ).show();
  }
}
