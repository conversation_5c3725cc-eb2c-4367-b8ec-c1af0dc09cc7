import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// نظام الخطوط والنصوص المتطور لتطبيق Zaen Smart Home
/// يوفر أنماط نصوص متسقة ومتجاوبة مع الوضع الداكن والفاتح
class AppTypography {
  // === عائلات الخطوط ===

  /// الخط الأساسي للتطبيق
  static const String primaryFontFamily = 'Cairo';

  /// خط ثانوي للعناوين
  static const String secondaryFontFamily = 'Tajawal';

  /// خط أحادي المسافة للأرقام والكود
  static const String monospaceFontFamily = 'Courier New';

  // === أحجام الخطوط ===

  /// أحجام صغيرة
  static const double fontSizeXs = 10.0;
  static const double fontSizeS = 12.0;

  /// أحجام متوسطة
  static const double fontSizeM = 14.0;
  static const double fontSizeL = 16.0;
  static const double fontSizeXl = 18.0;

  /// أحجام كبيرة
  static const double fontSizeXxl = 20.0;
  static const double fontSizeXxxl = 24.0;

  /// أحجام العناوين
  static const double fontSizeH1 = 32.0;
  static const double fontSizeH2 = 28.0;
  static const double fontSizeH3 = 24.0;
  static const double fontSizeH4 = 20.0;
  static const double fontSizeH5 = 18.0;
  static const double fontSizeH6 = 16.0;

  // === أوزان الخطوط ===

  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;

  // === ارتفاع الأسطر ===

  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.4;
  static const double lineHeightRelaxed = 1.6;
  static const double lineHeightLoose = 1.8;

  // === تباعد الأحرف ===

  static const double letterSpacingTight = -0.5;
  static const double letterSpacingNormal = 0.0;
  static const double letterSpacingWide = 0.5;
  static const double letterSpacingExtraWide = 1.0;

  // === أنماط العناوين ===

  /// عنوان رئيسي كبير
  static TextStyle get h1 => TextStyle(
        fontSize: fontSizeH1,
        fontWeight: fontWeightBold,
        fontFamily: secondaryFontFamily,
        color: AppColors.textColor,
        height: lineHeightTight,
        letterSpacing: letterSpacingTight,
      );

  /// عنوان رئيسي متوسط
  static TextStyle get h2 => TextStyle(
        fontSize: fontSizeH2,
        fontWeight: fontWeightBold,
        fontFamily: secondaryFontFamily,
        color: AppColors.textColor,
        height: lineHeightTight,
        letterSpacing: letterSpacingNormal,
      );

  /// عنوان فرعي كبير
  static TextStyle get h3 => TextStyle(
        fontSize: fontSizeH3,
        fontWeight: fontWeightSemiBold,
        fontFamily: secondaryFontFamily,
        color: AppColors.textColor,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// عنوان فرعي متوسط
  static TextStyle get h4 => TextStyle(
        fontSize: fontSizeH4,
        fontWeight: fontWeightSemiBold,
        fontFamily: primaryFontFamily,
        color: AppColors.textColor,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// عنوان فرعي صغير
  static TextStyle get h5 => TextStyle(
        fontSize: fontSizeH5,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.textPrimary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// عنوان صغير
  static TextStyle get h6 => TextStyle(
        fontSize: fontSizeH6,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.textPrimary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  // === أنماط النصوص الأساسية ===

  /// نص أساسي كبير
  static TextStyle get bodyLarge => TextStyle(
        fontSize: fontSizeL,
        fontWeight: fontWeightRegular,
        fontFamily: primaryFontFamily,
        color: AppColors.textPrimary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// نص أساسي متوسط
  static TextStyle get bodyMedium => TextStyle(
        fontSize: fontSizeM,
        fontWeight: fontWeightRegular,
        fontFamily: primaryFontFamily,
        color: AppColors.textPrimary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// نص أساسي صغير
  static TextStyle get bodySmall => TextStyle(
        fontSize: fontSizeS,
        fontWeight: fontWeightRegular,
        fontFamily: primaryFontFamily,
        color: AppColors.textSecondary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  // === أنماط التسميات والأزرار ===

  /// تسمية كبيرة
  static TextStyle get labelLarge => TextStyle(
        fontSize: fontSizeM,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.textPrimary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingWide,
      );

  /// تسمية متوسطة
  static TextStyle get labelMedium => TextStyle(
        fontSize: fontSizeS,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.textPrimary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingWide,
      );

  /// تسمية صغيرة
  static TextStyle get labelSmall => TextStyle(
        fontSize: fontSizeXs,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.textSecondary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingExtraWide,
      );

  // === أنماط الأزرار ===

  /// نص زر كبير
  static TextStyle get buttonLarge => TextStyle(
        fontSize: fontSizeL,
        fontWeight: fontWeightSemiBold,
        fontFamily: primaryFontFamily,
        color: AppColors.current.onPrimary,
        height: lineHeightTight,
        letterSpacing: letterSpacingWide,
      );

  /// نص زر متوسط
  static TextStyle get buttonMedium => TextStyle(
        fontSize: fontSizeM,
        fontWeight: fontWeightSemiBold,
        fontFamily: primaryFontFamily,
        color: AppColors.current.onPrimary,
        height: lineHeightTight,
        letterSpacing: letterSpacingWide,
      );

  /// نص زر صغير
  static TextStyle get buttonSmall => TextStyle(
        fontSize: fontSizeS,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.current.onPrimary,
        height: lineHeightTight,
        letterSpacing: letterSpacingWide,
      );

  // === أنماط خاصة ===

  /// نص تسمية توضيحية
  static TextStyle get caption => TextStyle(
        fontSize: fontSizeXs,
        fontWeight: fontWeightRegular,
        fontFamily: primaryFontFamily,
        color: AppColors.subtitleColor,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// نص مبالغ فيه
  static TextStyle get overline => TextStyle(
        fontSize: fontSizeXs,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.textSecondary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingExtraWide,
      );

  /// نص أحادي المسافة للأرقام
  static TextStyle get monospace => TextStyle(
        fontSize: fontSizeM,
        fontWeight: fontWeightRegular,
        fontFamily: monospaceFontFamily,
        color: AppColors.textPrimary,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// نص للأخطاء
  static TextStyle get error => TextStyle(
        fontSize: fontSizeS,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.error,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// نص للنجاح
  static TextStyle get success => TextStyle(
        fontSize: fontSizeS,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.success,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// نص للتحذير
  static TextStyle get warning => TextStyle(
        fontSize: fontSizeS,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.warning,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  /// نص للمعلومات
  static TextStyle get info => TextStyle(
        fontSize: fontSizeS,
        fontWeight: fontWeightMedium,
        fontFamily: primaryFontFamily,
        color: AppColors.info,
        height: lineHeightNormal,
        letterSpacing: letterSpacingNormal,
      );

  // === وظائف مساعدة ===

  /// إنشاء نمط نص مخصص
  static TextStyle custom({
    double? fontSize,
    FontWeight? fontWeight,
    String? fontFamily,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
  }) {
    return TextStyle(
      fontSize: fontSize ?? fontSizeM,
      fontWeight: fontWeight ?? fontWeightRegular,
      fontFamily: fontFamily ?? primaryFontFamily,
      color: color ?? AppColors.textPrimary,
      height: height ?? lineHeightNormal,
      letterSpacing: letterSpacing ?? letterSpacingNormal,
      decoration: decoration,
      decorationColor: decorationColor,
      decorationStyle: decorationStyle,
    );
  }

  /// تطبيق لون مخصص على نمط موجود
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  /// تطبيق حجم مخصص على نمط موجود
  static TextStyle withSize(TextStyle style, double fontSize) {
    return style.copyWith(fontSize: fontSize);
  }

  /// تطبيق وزن مخصص على نمط موجود
  static TextStyle withWeight(TextStyle style, FontWeight fontWeight) {
    return style.copyWith(fontWeight: fontWeight);
  }

  /// تطبيق عائلة خط مخصصة على نمط موجود
  static TextStyle withFamily(TextStyle style, String fontFamily) {
    return style.copyWith(fontFamily: fontFamily);
  }

  /// تطبيق تزيين على نمط موجود
  static TextStyle withDecoration(
    TextStyle style,
    TextDecoration decoration, {
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
  }) {
    return style.copyWith(
      decoration: decoration,
      decorationColor: decorationColor,
      decorationStyle: decorationStyle,
    );
  }

  /// الحصول على TextTheme كامل للتطبيق
  static TextTheme get textTheme => TextTheme(
        displayLarge: h1,
        displayMedium: h2,
        displaySmall: h3,
        headlineLarge: h4,
        headlineMedium: h5,
        headlineSmall: h6,
        titleLarge: h4,
        titleMedium: h5,
        titleSmall: h6,
        bodyLarge: bodyLarge,
        bodyMedium: bodyMedium,
        bodySmall: bodySmall,
        labelLarge: labelLarge,
        labelMedium: labelMedium,
        labelSmall: labelSmall,
      );

  /// أنماط نصوص خاصة بالتطبيق

  /// نص عنوان الصفحة
  static TextStyle get pageTitle => h2.copyWith(
        color: AppColors.primaryColor,
        fontWeight: fontWeightBold,
      );

  /// نص عنوان الكارت
  static TextStyle get cardTitle => h5.copyWith(
        fontWeight: fontWeightSemiBold,
      );

  /// نص وصف الكارت
  static TextStyle get cardSubtitle => bodySmall.copyWith(
        color: AppColors.subtitleColor,
      );

  /// نص حالة الجهاز
  static TextStyle get deviceStatus => labelMedium.copyWith(
        fontWeight: fontWeightBold,
      );

  /// نص قيمة الاستشعار
  static TextStyle get sensorValue => monospace.copyWith(
        fontSize: fontSizeXl,
        fontWeight: fontWeightBold,
        color: AppColors.primaryColor,
      );

  /// نص وحدة القياس
  static TextStyle get unit => caption.copyWith(
        color: AppColors.subtitleColor,
      );

  /// نص رابط
  static TextStyle get link => bodyMedium.copyWith(
        color: AppColors.primaryColor,
        decoration: TextDecoration.underline,
      );

  /// نص مميز
  static TextStyle get highlight => bodyMedium.copyWith(
        backgroundColor: AppColors.accentColor.withOpacity(0.2),
        color: AppColors.accentColor,
        fontWeight: fontWeightMedium,
      );
}
