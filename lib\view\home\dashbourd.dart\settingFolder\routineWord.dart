import 'dart:convert';

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/view/home/<USER>/routine.dart';
import 'package:zaen/shared/themes/app_colors.dart';

routineWords(context, setState) async {
  if (client.connectionStatus!.state.name == 'connected') {
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));

    var appDB = await openDatabase('${controller.system}.db', version: 3);

    // Map<String, String> rooms = {};
    // Results appdevices = await conn.query("SELECT id,Type,rooms FROM Devices");
    // print(appdevices);
    // Map devices = {};

    // for (var r = 0; r < controller.rooms.keys.toList().length; r++) {
    //   rooms[controller.rooms[controller.rooms.keys.toList()[r]]['id']] =
    //       controller.rooms[controller.rooms.keys.toList()[r]]['privName'];
    //   for (var d in controller
    //       .rooms[controller.rooms.keys.toList()[r]]['devices'].values) {
    //     devices[d['id']] =
    //         controller.rooms[controller.rooms.keys.toList()[r]]['id'];
    //   }
    // }
    // for (var r in appdevices) {
    //   if (r.fields['rooms'] == 'x') {
    //     devices[r.fields['id']] = 'x';
    //   }
    // }
    // Map<String, String> rooms1 = {};
    // rooms1['x'] = 'بدون غرفة';
    // rooms1.addAll(rooms);
    // print(devices);
    // print('1111111111111111111111111111111111111');

    AwesomeDialog(
        context: context,
        dialogType: DialogType.noHeader,
        headerAnimationLoop: true,
        animType: AnimType.topSlide,
        dialogBackgroundColor: AppColors.backgroundColor2,
        width: controller.sizedWidth,
        padding: EdgeInsets.only(
            bottom: controller.sizedHight * 0.02,
            right: controller.sizedWidth * 0.01,
            left: controller.sizedWidth * 0.01),
        body: GetBuilder<HomeController>(
            builder: ((controller) => Material(
                color: Colors.transparent,
                child: Column(
                  children: [
                    txtStyle(txt: 'الكلمات الروتينية'),
                    SingleChildScrollView(
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () async {
                              if (Navigator.of(context).canPop()) {
                                Navigator.of(context).pop();
                              }
                              await 0.3.delay();
                              setState(() {
                                p = 1;
                                controller.addRoutine.clear();
                                isSetting = true;
                                isShortcut = false;
                                isTask = false;
                                isWords = true;
                                add = true;
                                myId = '';
                              });
                              pageController.jumpToPage(p);
                            },
                            child: containerIconsOption(
                              radius: 10,
                              margin: EdgeInsets.all(controller.sized * 0.01),
                              padding: EdgeInsets.all(controller.sized * 0.002),
                              content: Container(
                                width: double.infinity,
                                child: iconStyle(
                                    icon: Icons.add_circle_rounded,
                                    color: AppColors.primaryColor.withOpacity(0.5),
                                    size: controller.sized * 0.025),
                              ),
                            ),
                          ),
                          for (var task in controller.routineWords!)
                            GestureDetector(
                              onTap: () async {
                                if (Navigator.of(context).canPop()) {
                                  Navigator.of(context).pop();
                                }
                                await 0.3.delay();
                                setState(() {
                                  p = 0;
                                  add = false;

                                  controller.addRoutine =
                                      json.decode(task.fields['route']);
                                  isSetting = true;
                                  isShortcut = false;
                                  isTask = false;
                                  name2.text = task.fields['word'];
                                  myId = task.fields['id'].toString();
                                  isWords = true;
                                });
                                pageController.jumpToPage(p);
                              },
                              child: containerIconsOption(
                                  radius: 10,
                                  margin:
                                      EdgeInsets.all(controller.sized * 0.005),
                                  padding:
                                      EdgeInsets.all(controller.sized * 0.002),
                                  content: Directionality(
                                    textDirection: TextDirection.rtl,
                                    child: Container(
                                        width: double.infinity,
                                        height: controller.sizedHight * 0.04,
                                        padding: EdgeInsets.symmetric(
                                            horizontal:
                                                controller.sizedWidth * 0.02),
                                        child: Center(
                                            child: txtStyle(
                                                txt: task.fields['word']))),
                                  )),
                            ),
                        ],
                      ),
                    ),
                  ],
                ))))

        // btnCancelOnPress:
        //     () {},
        ).show();
  }
}
