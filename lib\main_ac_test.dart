import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:zaen/shared/themes/theme_manager.dart';
import 'package:zaen/view/test/ac_design_test.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة كونترولر الإعدادات
  Get.put(SettingsController());

  runApp(const ACTestApp());
}

class ACTestApp extends StatelessWidget {
  const ACTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'AC Design Test',
      theme: ThemeManager.lightTheme,
      darkTheme: ThemeManager.darkTheme,
      themeMode: ThemeMode.system,
      home: const ACDesignTestPage(),
    );
  }
}
