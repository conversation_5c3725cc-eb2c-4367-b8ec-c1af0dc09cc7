import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/themes/app_typography.dart';
import 'package:zaen/shared/themes/app_dimensions.dart';
import 'package:zaen/shared/settings/settings.dart';

/// مدير الثيمات المتطور لتطبيق Zaen Smart Home
/// يوفر ثيمات متكاملة للوضع الداكن والفاتح مع تصميم حديث
class ThemeManager {
  /// الحصول على الثيم الحالي بناءً على الإعدادات
  static ThemeData get currentTheme {
    try {
      final isDark = Get.find<SettingsController>().isDarkMode.value;
      return isDark ? darkTheme : lightTheme;
    } catch (e) {
      // في حالة عدم توفر الكونترولر، استخدم الثيم الفاتح كافتراضي
      return lightTheme;
    }
  }

  /// الثيم الفاتح - تصميم حديث وأنيق
  static ThemeData get lightTheme => ThemeData(
        brightness: Brightness.light,
        primarySwatch: Colors.teal,
        primaryColor: AppColors.primary,
        scaffoldBackgroundColor: AppColors.backgroundPrimary,

        // نظام الألوان المحدث مع الألوان الجديدة
        colorScheme: ColorScheme.light(
          primary: AppColors.primary,
          primaryContainer: AppColors.primaryDark,
          secondary: AppColors.secondary,
          secondaryContainer: AppColors.accent,
          surface: AppColors.surface,
          surfaceContainerHighest: AppColors.surfaceElevated,
          error: AppColors.error,
          onPrimary: AppColors.white,
          onSecondary: AppColors.white,
          onSurface: AppColors.textPrimary,
          onSurfaceVariant: AppColors.textSecondary,
          onError: AppColors.white,
          outline: AppColors.border,
          outlineVariant: AppColors.borderLight,
          shadow: AppColors.shadow,
          scrim: AppColors.overlay,
        ),

        // شريط التطبيق
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.surface,
          foregroundColor: AppColors.textPrimary,
          elevation: AppDimensions.elevationLow,
          centerTitle: true,
          titleTextStyle: AppTypography.h5.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(AppDimensions.radiusM),
              bottomRight: Radius.circular(AppDimensions.radiusM),
            ),
          ),
          iconTheme: IconThemeData(
            color: AppColors.textPrimary,
            size: AppDimensions.iconM,
          ),
        ),

        // الكروت
        cardTheme: CardTheme(
          color: AppColors.surface,
          elevation: AppDimensions.elevationMedium,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusM,
          ),
          margin: AppDimensions.marginS,
        ),

        // النصوص
        textTheme: AppTypography.textTheme,

        // الأزرار المرفوعة
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            elevation: AppDimensions.elevationLow,
            minimumSize:
                Size(AppDimensions.buttonWidthM, AppDimensions.buttonHeightM),
            shape: RoundedRectangleBorder(
              borderRadius: AppDimensions.borderRadiusM,
            ),
            padding: AppDimensions.paddingM,
          ),
        ),

        // الأزرار المحددة
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: AppColors.primary,
            side: BorderSide(
              color: AppColors.border,
              width: AppDimensions.borderWidthNormal,
            ),
            minimumSize:
                Size(AppDimensions.buttonWidthM, AppDimensions.buttonHeightM),
            shape: RoundedRectangleBorder(
              borderRadius: AppDimensions.borderRadiusM,
            ),
            padding: AppDimensions.paddingM,
          ),
        ),

        // أزرار النص
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: AppColors.primary,
            minimumSize:
                Size(AppDimensions.buttonWidthS, AppDimensions.buttonHeightS),
            shape: RoundedRectangleBorder(
              borderRadius: AppDimensions.borderRadiusS,
            ),
            padding: AppDimensions.paddingS,
          ),
        ),

        // زر الإجراء العائم
        floatingActionButtonTheme: FloatingActionButtonThemeData(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          elevation: AppDimensions.elevationMedium,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusL,
          ),
        ),

        // حقول الإدخال
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.surfaceElevated,
          border: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.border,
              width: AppDimensions.borderWidthNormal,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.border,
              width: AppDimensions.borderWidthNormal,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.primary,
              width: AppDimensions.borderWidthThick,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.error,
              width: AppDimensions.borderWidthNormal,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.error,
              width: AppDimensions.borderWidthThick,
            ),
          ),
          contentPadding: AppDimensions.paddingM,
          labelStyle: AppTypography.labelMedium,
          hintStyle: AppTypography.bodySmall.copyWith(
            color: AppColors.textHint,
          ),
          errorStyle: AppTypography.error,
        ),

        // الأيقونات العامة
        iconTheme: IconThemeData(
          color: AppColors.textPrimary,
          size: AppDimensions.iconM,
        ),

        // المفاتيح
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return AppColors.primary;
            }
            return AppColors.textSecondary;
          }),
          trackColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return AppColors.primary.withOpacity(0.6);
            }
            return AppColors.border;
          }),
        ),

        // أشرطة التقدم
        progressIndicatorTheme: ProgressIndicatorThemeData(
          color: AppColors.primary,
          linearTrackColor: AppColors.border,
          circularTrackColor: AppColors.border,
        ),

        // الفواصل
        dividerTheme: DividerThemeData(
          color: AppColors.border,
          thickness: AppDimensions.borderWidthThin,
          space: AppDimensions.spacingM,
        ),
      );

  /// الثيم الداكن - تصميم أنيق ومريح للعين
  static ThemeData get darkTheme => ThemeData(
        brightness: Brightness.dark,
        primarySwatch: Colors.teal,
        primaryColor: AppColors.primary,
        scaffoldBackgroundColor: AppColors.backgroundPrimary,

        // نظام الألوان المحدث مع الألوان الجديدة
        colorScheme: ColorScheme.dark(
          primary: AppColors.primary,
          primaryContainer: AppColors.primaryDark,
          secondary: AppColors.secondary,
          secondaryContainer: AppColors.accent,
          surface: AppColors.surface,
          surfaceContainerHighest: AppColors.surfaceElevated,
          error: AppColors.error,
          onPrimary: AppColors.white,
          onSecondary: AppColors.white,
          onSurface: AppColors.textPrimary,
          onSurfaceVariant: AppColors.textSecondary,
          onError: AppColors.white,
          outline: AppColors.border,
          outlineVariant: AppColors.borderLight,
          shadow: AppColors.shadow,
          scrim: AppColors.overlay,
        ),

        // شريط التطبيق
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.surface,
          foregroundColor: AppColors.textPrimary,
          elevation: AppDimensions.elevationLow,
          centerTitle: true,
          titleTextStyle: AppTypography.h5.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(AppDimensions.radiusM),
              bottomRight: Radius.circular(AppDimensions.radiusM),
            ),
          ),
          iconTheme: IconThemeData(
            color: AppColors.textPrimary,
            size: AppDimensions.iconM,
          ),
        ),

        // الكروت
        cardTheme: CardTheme(
          color: AppColors.surface,
          elevation: AppDimensions.elevationMedium,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusM,
          ),
          margin: AppDimensions.marginS,
        ),

        // النصوص
        textTheme: AppTypography.textTheme,

        // الأزرار المرفوعة
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            elevation: AppDimensions.elevationLow,
            minimumSize:
                Size(AppDimensions.buttonWidthM, AppDimensions.buttonHeightM),
            shape: RoundedRectangleBorder(
              borderRadius: AppDimensions.borderRadiusM,
            ),
            padding: AppDimensions.paddingM,
          ),
        ),

        // الأزرار المحددة
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: AppColors.primary,
            side: BorderSide(
              color: AppColors.border,
              width: AppDimensions.borderWidthNormal,
            ),
            minimumSize:
                Size(AppDimensions.buttonWidthM, AppDimensions.buttonHeightM),
            shape: RoundedRectangleBorder(
              borderRadius: AppDimensions.borderRadiusM,
            ),
            padding: AppDimensions.paddingM,
          ),
        ),

        // أزرار النص
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: AppColors.primary,
            minimumSize:
                Size(AppDimensions.buttonWidthS, AppDimensions.buttonHeightS),
            shape: RoundedRectangleBorder(
              borderRadius: AppDimensions.borderRadiusS,
            ),
            padding: AppDimensions.paddingS,
          ),
        ),

        // زر الإجراء العائم
        floatingActionButtonTheme: FloatingActionButtonThemeData(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          elevation: AppDimensions.elevationMedium,
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusL,
          ),
        ),

        // حقول الإدخال
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.surfaceElevated,
          border: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.border,
              width: AppDimensions.borderWidthNormal,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.border,
              width: AppDimensions.borderWidthNormal,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.primary,
              width: AppDimensions.borderWidthThick,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.error,
              width: AppDimensions.borderWidthNormal,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: AppDimensions.borderRadiusM,
            borderSide: BorderSide(
              color: AppColors.error,
              width: AppDimensions.borderWidthThick,
            ),
          ),
          contentPadding: AppDimensions.paddingM,
          labelStyle: AppTypography.labelMedium,
          hintStyle: AppTypography.bodySmall.copyWith(
            color: AppColors.textHint,
          ),
          errorStyle: AppTypography.error,
        ),

        // الأيقونات العامة
        iconTheme: IconThemeData(
          color: AppColors.textPrimary,
          size: AppDimensions.iconM,
        ),

        // المفاتيح
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return AppColors.primary;
            }
            return AppColors.textSecondary;
          }),
          trackColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return AppColors.primary.withOpacity(0.6);
            }
            return AppColors.border;
          }),
        ),

        // أشرطة التقدم
        progressIndicatorTheme: ProgressIndicatorThemeData(
          color: AppColors.primary,
          linearTrackColor: AppColors.border,
          circularTrackColor: AppColors.border,
        ),

        // الفواصل
        dividerTheme: DividerThemeData(
          color: AppColors.border,
          thickness: AppDimensions.borderWidthThin,
          space: AppDimensions.spacingM,
        ),
      );
}
