import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:network_info_plus/network_info_plus.dart';

import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:zaen/modules/local/database.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/themes/app_colors.dart';

class HomeController extends GetxController {
  Map rooms = {};
  Map alarms = {};
  Map roomData = {};
  Map addRoutine = {};
  List routines = [];
  List favorite = [];
  Results? tasks;
  Results? routineWords;
  RxString hostZain = ''.obs;
  RxString home = ''.obs;
  RxString homeType = ''.obs;
  RxString homeImage = ''.obs;
  String homeId = '';
  var homeState = null;
  bool reg = false;
  String apName = '';
  String apMAC = '';
  String sysMAC = '';
  var uuid;
  var deviceName;
  var deviceModel;
  List systems = [];
  String system = '';
  String wifi = '';
  Timer? _timer;
  Map devices = {'home': false};
  double sizedWidth = 0;
  double sizedHight = 0;
  double sized = 0;

  void start(homex, homeTypex, homeImagex, homeStatex, roomsx, homeIdx) {
    home.value = homex;
    homeType.value = homeTypex;
    homeImage.value = homeImagex;
    homeState = homeStatex;
    homeId = homeIdx;
    rooms = roomsx;
    update();
    print(rooms);
    print('55555555555555555555555555555');
  }

  Future<void> _getDeviceUuid() async {
    final prefs = await SharedPreferences.getInstance();
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    controller.uuid = prefs.getString('device_uuid');
    controller.deviceName = prefs.getString('device_name');
    var deviceModel1 = prefs.getString('device_model');

    if (uuid == null) {
      uuid = Uuid().v4();
      await prefs.setString('device_uuid', uuid);
    }
    if (deviceName == null) {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceName = androidInfo.device;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceName = iosInfo.name;
      } else {
        deviceName = 'غير معروف';
      }
      await prefs.setString('device_name', deviceName);
    }
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceModel = androidInfo.device;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceModel = iosInfo.model;
    } else {
      deviceModel = 'غير معروف';
    }
    if (deviceModel1 == null || deviceModel1 != deviceModel) {
      await prefs.setString('device_model', deviceModel);
    }
  }

  Future<void> _checkConnectionStatus() async {
    try {
      final info = NetworkInfo();

      var wifiName = await info.getWifiName();
      final wifiBSSID = await info.getWifiBSSID();
      if (wifiName != wifi) {
        if (reg) {
          reg = false;
          if (systems.isEmpty) {
            Get.toNamed('wait');
          } else {
            Get.back();
          }
        }
        if (wifiName != null) {
          wifi = wifiName;
        } else {
          wifi = '';
        }
        controller.update();
      }
      if (reg == false &&
          wifiName != null &&
          wifiName.toUpperCase().contains(wifiBSSID!.toUpperCase())) {
        controller.wifi = wifiName;
        controller.reg = true;
        controller.apMAC = wifiBSSID;
        controller.apName = wifiName.replaceAll('"', '').split('/')[0];
        Get.toNamed('reg');
      } else if (reg == false && wifiName != null && hostZain.isEmpty) {
        await findIp();
        if (hostZain.isNotEmpty) {
          await connect();
          await client.subscribe(controller.uuid, MqttQos.atLeastOnce);

          final builder = MqttClientPayloadBuilder();
          builder.addString(controller.uuid);
          client.publishMessage(
              'phone/ask', MqttQos.atLeastOnce, builder.payload!);
        }
        // await connect();
      } else if (wifiName == null && hostZain.isNotEmpty) {
        hostZain.value = '';
      }
    } catch (e) {
      print('444444444444444444444444');
      print(e);
    }
  }

  @override
  void onInit() async {
    await _getDeviceUuid();
    await getSystems();
    print(systems);
    print('888888888888888888888888888');
    final info = NetworkInfo();
    final wifiName = await info.getWifiName();
    final wifiBSSID = await info.getWifiBSSID();
    if (wifiName != null) {
      wifi = wifiName;
      await findIp();
      if (hostZain.isNotEmpty) {
        await connect();
        await client.subscribe(controller.uuid, MqttQos.atLeastOnce);

        final builder = MqttClientPayloadBuilder();
        builder.addString(controller.uuid);
        client.publishMessage(
            'phone/ask', MqttQos.atLeastOnce, builder.payload!);
      }
    }
    if (systems.isNotEmpty && system != '') {
      try {
        if (wifi.isNotEmpty) {
          print('88888888');
          await findIp();
        }
        if (!reg) {
          if (hostZain.value.isEmpty) {
            // يتم الدخول الى اقرب نظام
            final response =
                await http.get(Uri.parse('https://www.google.com'));
            if (response.statusCode == 200) {
              print('888888888888888888');
            }
          } else {
            // يتم الاتصال ببروتوكول mqtt
            // ارسال استفسار
            // عندما يتم الرد يتم فحص اذا الجهاز موجود في قاعده بيانات الهاتف
            // اذا كان موجود يتم الدخول
            // اذا لم يكن موجود
            // يتم الاتصال باقرب نظام

            await connect();
            await client.subscribe(controller.uuid, MqttQos.atLeastOnce);

            final builder = MqttClientPayloadBuilder();
            builder.addString(controller.uuid);
            client.publishMessage(
                'phone/ask', MqttQos.atLeastOnce, builder.payload!);
            await getDevices();
            // controller.update();
            // Get.toNamed('home');
          }
        }
      } catch (e) {
        print('error 7777777777777');
      }
    } else {
      if (wifiName != null &&
          wifiName.toUpperCase().contains(wifiBSSID!.toUpperCase())) {
        controller.wifi = wifiName;
        controller.reg = true;
        controller.apMAC = wifiBSSID;
        controller.apName = wifiName.replaceAll('"', '').split('/')[0];
        Get.toNamed('reg');
      } else {
        Get.toNamed('wait');
      }
    }

    _timer = Timer.periodic(Duration(seconds: 7), (timer) {
      _checkConnectionStatus();
    });
    // await appDatabase();

    // print(client.connectionStatus!.state.name);

    // print('init');

    super.onInit();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Future<void> onReady() async {
    // print('ready');
    // while (!reg) {
    //   try {
    //     await client.subscribe(homeId + "/app/phone", MqttQos.atLeastOnce);
    //     await client.subscribe("edit", MqttQos.atLeastOnce);
    //     await client.subscribe(controller.uuid, MqttQos.atLeastOnce);

    //     break;
    //   } catch (e) {}
    // }

    super.onReady();
  }
}
