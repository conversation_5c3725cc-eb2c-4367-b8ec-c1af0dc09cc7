import 'package:flutter/material.dart';
import 'package:zaen/shared/commands/tv.dart';
import 'package:zaen/shared/components/components.dart';
import '../../../../shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget ZAEN({
  device,
  room,
  var volume = 20,
  var hearing = 15,
  bool sil = true,
  bool lestin = true,
  bool play = true,
  bool Zmain = false,
  bool connect = true,
  String ZName = 'زين',
  required Function() TapPlay,
  required Function() Play,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_hearingMute,
  required Function() tapOn_Switch_Icon,
  required Function(double?) vState,
  required Function(double?) hState,
}) {
  print(device);
  roomId = room;

  return Directionality(
    textDirection: TextDirection.rtl,
    child: Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.all(controller.sized * 0.01),
                child: iconStyle(icon: Icons.flutter_dash, color: AppColors.warningColor),
              ),
              Container(
                margin: EdgeInsets.zero,
                // padding:
                //     EdgeInsets.only(right: controller.sizedWidth * 0.01),
                // color: Colors.blueGrey.shade600,
                // alignment: Alignment.bottomRight,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.only(
                            left: controller.sizedWidth * 0.008,
                          ),
                          decoration: BoxDecoration(
                              border: Border(
                                  left: BorderSide(
                                      color: AppColors.textColor3.withOpacity(0.45),
                                      width: 1))),
                          child: txtStyle(
                            align: TextAlign.right,
                            txt: 'مساعد صوتي',
                          ),
                        ),
                        Container(
                            width: controller.sizedWidth * 0.27,
                            padding: EdgeInsets.only(
                                right: controller.sizedWidth * 0.01),
                            child: txtStyle(
                              align: TextAlign.right,
                              txt: ZName,
                            )),
                      ],
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.005,
                    ),
                    Row(
                      textDirection: TextDirection.rtl,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        txtStyle(txt: controller.rooms[room]['privName']),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.all(controller.sized * 0.008),
                child: Zmain
                    ? Padding(
                        padding: EdgeInsets.only(
                            right: controller.sizedWidth * 0.01),
                        child: IconButton(
                          onPressed: tapOn_Switch_Icon,
                          icon: iconStyle(
                              icon: Icons.star_rounded, color: AppColors.warningColor),
                        ))
                    : Container(),
              )
            ],
          ),
        ),
        SizedBox(
          height: controller.sizedHight * 0.02,
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              IconButton(
                onPressed: Play,
                padding: EdgeInsets.zero,
                icon: iconStyle(
                    icon: Icons.fast_forward_rounded,
                    size: controller.sized * 0.04),
              ),
              SizedBox(
                width: controller.sizedWidth * 0.07,
              ),
              IconButton(
                padding: EdgeInsets.zero,
                onPressed: TapPlay,
                icon: iconStyle(
                    icon: play ? Icons.play_arrow_rounded : Icons.pause_rounded,
                    size: controller.sized * 0.04),
              ),
              SizedBox(
                width: controller.sizedWidth * 0.07,
              ),
              IconButton(
                onPressed: Play,
                padding: EdgeInsets.zero,
                icon: iconStyle(
                    icon: Icons.fast_rewind_rounded,
                    size: controller.sized * 0.04),
              ),
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: controller.sizedWidth * 0.06),
                child: Slider(
                    min: 0,
                    max: 30,
                    activeColor: AppColors.textColor.withOpacity(0.7),
                    inactiveColor: AppColors.textColor.withOpacity(0.15),
                    thumbColor: AppColors.textColor.withOpacity(0.95),
                    value: volume.toDouble(),
                    onChanged: vState),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: controller.sizedWidth * 0.025),
              child: IconButton(
                padding: EdgeInsets.zero,
                onPressed: tapOn_VolumeMute,
                iconSize: controller.sized * 0.032,
                icon: iconStyle(
                  icon:
                      sil ? Icons.volume_up_rounded : Icons.volume_off_rounded,
                  color: AppColors.textColor.withOpacity(0.75),
                ),
              ),
            ),
          ],
        ),
        Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(right: controller.sized * 0.02),
                  child: Slider(
                      min: 0,
                      max: 30,
                      activeColor: AppColors.textColor.withOpacity(0.7),
                      inactiveColor: AppColors.textColor.withOpacity(0.15),
                      thumbColor: AppColors.textColor.withOpacity(0.95),
                      value: hearing.toDouble(),
                      onChanged: hState),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: controller.sizedWidth * 0.025),
                child: IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: tapOn_hearingMute,
                  iconSize: controller.sized * 0.032,
                  icon: iconStyle(
                    icon: lestin
                        ? Icons.hearing_rounded
                        : Icons.hearing_disabled_rounded,
                    color: AppColors.textColor.withOpacity(0.75),
                  ),
                ),
              ),
            ])
      ],
    ),
  );
}
