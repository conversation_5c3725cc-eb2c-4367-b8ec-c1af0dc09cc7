import 'package:flutter/material.dart';
import '../../../../shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'favorite_state.dart';
import 'favorite_device_builder.dart';

Widget favorite() {
  // Debug prints removed for production

  return StatefulBuilder(builder: ((context, setState) {
    return Center(
      child: SizedBox(
        height: controller.sizedHight * 0.38,
        child: PageView.builder(
            scrollDirection: Axis.vertical,
            physics: const BouncingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            controller: PageController(),
            onPageChanged: (pageIndex) {
              if (isExpanded) {
                onBoxTap(selectedDevice!, setState);
              }
            },
            itemCount: (controller.favorite.length / 4).ceil(),
            itemBuilder: (context, pageIndex) {
              final startIndex = pageIndex * 4;
              final endIndex = (pageIndex + 1) * 4;

              List stackDevices = controller.favorite.sublist(startIndex);
              if (stackDevices.length > 4) {
                stackDevices =
                    controller.favorite.sublist(startIndex, endIndex);
              }

              return Padding(
                padding: EdgeInsets.symmetric(
                    vertical: controller.sized * 0.005,
                    horizontal: controller.sized * 0.013),
                child: Stack(
                  children: [
                    for (int i = 0; i < stackDevices.length; i++)
                      for (var room in controller.rooms.keys)
                        if (controller.rooms[room]['devices'].keys
                            .contains(stackDevices[i]))
                          Positioned(
                            left: i % 2 == 0 ? 0 : null,
                            right: i % 2 == 1 ? 0 : null,
                            top: i < 2 ? 0 : null,
                            bottom: i >= 2 ? 0 : null,
                            child: GestureDetector(
                              onDoubleTap: () {
                                if (controller.rooms[room]['devices']
                                            [stackDevices[i]]['state'] !=
                                        null ||
                                    controller.rooms[room]['devices']
                                            [stackDevices[i]]['device'] ==
                                        'ZAIN-Main' ||
                                    isExpanded == true) {
                                  onBoxTap(stackDevices[i], setState);
                                }
                              },
                              child: AnimatedContainer(
                                duration: Duration(
                                    milliseconds:
                                        stackDevices[i] == selectedDevice &&
                                                isExpanded
                                            ? 650
                                            : stackDevices[i] == selectedDevice
                                                ? 500
                                                : isExpanded
                                                    ? 50
                                                    : 50),
                                curve: stackDevices[i] == selectedDevice &&
                                        isExpanded
                                    ? Curves.easeOutCubic
                                    : Curves.easeInCubic,
                                margin: EdgeInsets.only(
                                  bottom: controller.sizedHight * 0.001,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.all(isExpanded
                                      ? const Radius.circular(45)
                                      : const Radius.circular(30)),
                                  color: AppColors.surfaceColor.withOpacity(
                                      isExpanded &&
                                              stackDevices[i] == selectedDevice
                                          ? 0.75
                                          : 0.6),
                                ),
                                width: isExpanded &&
                                        stackDevices[i] == selectedDevice
                                    ? controller.sizedWidth * 0.88
                                    : isExpanded &&
                                            stackDevices
                                                .contains(selectedDevice)
                                        ? 0
                                        : controller.sizedWidth * 0.3965,
                                height: isExpanded &&
                                        stackDevices[i] == selectedDevice
                                    ? controller.sizedHight * 0.36
                                    : isExpanded &&
                                            stackDevices
                                                .contains(selectedDevice)
                                        ? 0
                                        : controller.sizedHight * 0.168,
                                child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 300),
                                  switchInCurve: Curves.easeInOut,
                                  switchOutCurve: Curves.easeInOut,
                                  transitionBuilder: (Widget child,
                                      Animation<double> animation) {
                                    // تأثير انتقال مخصص يحافظ على استقرار باقي الحاويات
                                    return FadeTransition(
                                      opacity: animation,
                                      child: SlideTransition(
                                        position: Tween<Offset>(
                                          begin: const Offset(0.0, 0.1),
                                          end: Offset.zero,
                                        ).animate(CurvedAnimation(
                                          parent: animation,
                                          curve: Curves.easeOutQuart,
                                        )),
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: Container(
                                    key: ValueKey(
                                        '${stackDevices[i]}_${isExpanded && stackDevices[i] == selectedDevice}'),
                                    child: isExpanded &&
                                            stackDevices[i] == selectedDevice
                                        ? FavoriteDeviceBuilder
                                            .buildExpandedDevice(
                                                stackDevices[i],
                                                room,
                                                i,
                                                stackDevices,
                                                context)
                                        : FavoriteDeviceBuilder
                                            .buildCollapsedDevice(
                                                stackDevices[i],
                                                room,
                                                i,
                                                stackDevices),
                                  ),
                                ),
                              ),
                            ),
                          ),
                  ],
                ),
              );
            }),
      ),
    );
  }));
}
