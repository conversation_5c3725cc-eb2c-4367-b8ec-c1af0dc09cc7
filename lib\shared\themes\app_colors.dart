import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/settings/settings.dart';

/// نظام الألوان الحديث والأنيق لتطبيق Zaen Smart Home
/// مصمم بعناية لتوفير تجربة بصرية متميزة ومريحة للعين
class AppColors {
  AppColors._(); // منع إنشاء كائنات من هذه الفئة

  // === الألوان الأساسية الحديثة ===

  /// اللون الأساسي - أزرق هادئ وأنيق
  static const Color _primaryLight = Color(0xFF1976D2); // أزرق جميل
  static const Color _primaryDark = Color(0xFF00ACC1);
  static Color get primary => _isDarkMode ? _primaryDark : _primaryLight;

  /// اللون الأساسي الفاتح
  static const Color _primaryLightVariant = Color(0xFF64B5F6); // أزرق فاتح
  static const Color _primaryDarkVariant = Color(0xFF26C6DA);
  static Color get primaryLight =>
      _isDarkMode ? _primaryDarkVariant : _primaryLightVariant;

  /// اللون الأساسي الداكن
  static const Color _primaryDarkLight = Color(0xFF0D47A1); // أزرق داكن
  static const Color _primaryDarkDark = Color(0xFF00838F);
  static Color get primaryDark =>
      _isDarkMode ? _primaryDarkDark : _primaryDarkLight;

  /// اللون الثانوي - بنفسجي راقي
  static const Color _secondaryLight = Color(0xFF7C4DFF); // بنفسجي جميل
  static const Color _secondaryDark = Color(0xFFAB47BC);
  static Color get secondary => _isDarkMode ? _secondaryDark : _secondaryLight;

  /// لون التمييز - برتقالي دافئ
  static const Color accent = Color(0xFFFF6F00);

  // === ألوان النصوص الذكية ===

  /// النص الأساسي - عالي التباين
  static const Color _textPrimaryLight = Color(0xFF1A1A1A); // أسود ناعم
  static const Color _textPrimaryDark = Color(0xFFF0F6FC);
  static Color get textPrimary =>
      _isDarkMode ? _textPrimaryDark : _textPrimaryLight;

  /// النص الثانوي - متوسط التباين
  static const Color _textSecondaryLight = Color(0xFF424242); // رمادي متوسط
  static const Color _textSecondaryDark = Color(0xFF8B949E);
  static Color get textSecondary =>
      _isDarkMode ? _textSecondaryDark : _textSecondaryLight;

  /// النص المساعد - منخفض التباين
  static const Color _textHintLight = Color(0xFF757575); // رمادي فاتح
  static const Color _textHintDark = Color(0xFF6E7681);
  static Color get textHint => _isDarkMode ? _textHintDark : _textHintLight;

  /// النص المعطل
  static const Color _textDisabledLight = Color(0xFFD0D7DE);
  static const Color _textDisabledDark = Color(0xFF484F58);
  static Color get textDisabled =>
      _isDarkMode ? _textDisabledDark : _textDisabledLight;

  // === ألوان الخلفيات الأنيقة ===

  /// الخلفية الأساسية
  static const Color _backgroundPrimaryLight =
      Color(0xFFF8F8F8); // سيلفر فاتح مائل على البياض
  static const Color _backgroundPrimaryDark = Color(0xFF0D1117);
  static Color get backgroundPrimary =>
      _isDarkMode ? _backgroundPrimaryDark : _backgroundPrimaryLight;

  /// الخلفية الثانوية
  static const Color _backgroundSecondaryLight =
      Color(0xFFEEEEEE); // سيلفر متوسط
  static const Color _backgroundSecondaryDark = Color(0xFF161B22);
  static Color get backgroundSecondary =>
      _isDarkMode ? _backgroundSecondaryDark : _backgroundSecondaryLight;

  /// خلفية الكروت والعناصر
  static const Color _surfaceLight = Color(0xFFFCFCFC); // أبيض سيلفر خفيف
  static const Color _surfaceDark = Color(0xFF21262D);
  static Color get surface => _isDarkMode ? _surfaceDark : _surfaceLight;

  /// خلفية مرتفعة (للعناصر البارزة)
  static const Color _surfaceElevatedLight =
      Color(0xFFF5F5F5); // سيلفر فاتح للعناصر البارزة
  static const Color _surfaceElevatedDark = Color(0xFF30363D);
  static Color get surfaceElevated =>
      _isDarkMode ? _surfaceElevatedDark : _surfaceElevatedLight;

  // === ألوان الحالة الحديثة ===

  /// النجاح - أخضر طبيعي
  static const Color success = Color(0xFF238636);
  static const Color successLight = Color(0xFF2EA043);
  static const Color successDark = Color(0xFF1A7F37);

  /// الخطأ - أحمر واضح
  static const Color error = Color(0xFFDA3633);
  static const Color errorLight = Color(0xFFFF6B6B);
  static const Color errorDark = Color(0xFFCF222E);

  /// التحذير - برتقالي دافئ
  static const Color warning = Color(0xFFD1242F);
  static const Color warningLight = Color(0xFFFF8A65);
  static const Color warningDark = Color(0xFFBF8700);

  /// المعلومات - أزرق هادئ
  static const Color info = Color(0xFF0969DA);
  static const Color infoLight = Color(0xFF54A3FF);
  static const Color infoDark = Color(0xFF0550AE);

  // === ألوان التفاعل ===

  /// الحدود والخطوط
  static const Color _borderLight = Color(0xFFDDDDDD); // سيلفر للحدود
  static const Color _borderDark = Color(0xFF30363D);
  static Color get border => _isDarkMode ? _borderDark : _borderLight;

  /// الحدود الفاتحة
  static const Color _borderLightVariant =
      Color(0xFFE8E8E8); // سيلفر فاتح للحدود
  static const Color _borderDarkVariant = Color(0xFF21262D);
  static Color get borderLight =>
      _isDarkMode ? _borderDarkVariant : _borderLightVariant;

  /// حدود مميزة للكونتينرات
  static const Color _borderAccentLight =
      Color(0xFFD0D0D0); // سيلفر متوسط للحدود
  static const Color _borderAccentDark = Color(0xFF4A5568);
  static Color get borderAccent =>
      _isDarkMode ? _borderAccentDark : _borderAccentLight;

  /// الظلال
  static const Color _shadowLight = Color(0x25000000);
  static const Color _shadowDark = Color(0x50000000);
  static Color get shadow => _isDarkMode ? _shadowDark : _shadowLight;

  /// التراكب (Overlay)
  static const Color overlay = Color(0x90000000);

  // === ألوان خاصة ===

  /// الأبيض النقي
  static const Color white = Color(0xFFFFFFFF);

  /// الأسود النقي
  static const Color black = Color(0xFF000000);

  /// شفاف
  static const Color transparent = Colors.transparent;

  // === خصائص مساعدة ===

  /// التحقق من الوضع الداكن
  static bool get _isDarkMode {
    try {
      return Get.find<SettingsController>().isDarkMode.value;
    } catch (e) {
      return false; // افتراضي للوضع الفاتح
    }
  }

  /// لون خاص لـ containerPageOption
  static Color get containerPageColor =>
      _isDarkMode ? backgroundColor3 : Colors.white;

  // === ألوان متدرجة جميلة ===

  /// تدرج أساسي
  static LinearGradient get primaryGradient => LinearGradient(
        colors: [primary, primaryLight],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );

  /// تدرج ثانوي
  static LinearGradient get secondaryGradient => LinearGradient(
        colors: [secondary, accent],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );

  /// تدرج الخلفية
  static LinearGradient get backgroundGradient => _isDarkMode
      ? LinearGradient(
          colors: [backgroundPrimary, backgroundSecondary],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        )
      : const LinearGradient(
          colors: [
            Color(0xFFFAFAFA), // سيلفر فاتح جداً مائل على البياض
            Color(0xFFF5F5F5), // سيلفر فاتح
            Color(0xFFEEEEEE), // سيلفر متوسط
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: [0.0, 0.5, 1.0],
        );

  /// تدرج النجاح
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, successLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// تدرج الخطأ
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, errorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// تدرج الكونتينرات للوضع الفاتح
  static LinearGradient get containerGradient => _isDarkMode
      ? LinearGradient(
          colors: [surface, surfaceElevated],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        )
      : const LinearGradient(
          colors: [
            Color(0xFFFDFDFD), // أبيض سيلفر خفيف جداً
            Color(0xFFF8F8F8), // سيلفر فاتح مائل على البياض
            Color(0xFFF0F0F0), // سيلفر فاتح
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: [0.0, 0.7, 1.0],
        );

  /// تدرج الكروت للوضع الفاتح
  static LinearGradient get cardGradient => _isDarkMode
      ? LinearGradient(
          colors: [surface, surfaceElevated],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        )
      : const LinearGradient(
          colors: [
            Color(0xFFFEFEFE), // أبيض سيلفر خفيف جداً
            Color(0xFFF9F9F9), // سيلفر فاتح جداً
            Color(0xFFF3F3F3), // سيلفر فاتح
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.6, 1.0],
        );

  /// تدرج خاص للكونتينرات المميزة
  static LinearGradient get specialContainerGradient => _isDarkMode
      ? LinearGradient(
          colors: [surfaceElevated, surface],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        )
      : const LinearGradient(
          colors: [
            Color(0xFFFBFBFB), // سيلفر فاتح جداً مائل على البياض
            Color(0xFFF6F6F6), // سيلفر فاتح
            Color(0xFFEDEDED), // سيلفر متوسط
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 0.5, 1.0],
        );

  // === للتوافق مع النظام القديم ===

  /// الألوان الأساسية القديمة
  static Color get primaryColor => primary;
  static Color get textColor => textPrimary;
  static Color get subtitleColor => textSecondary;
  static Color get textColor2 => textSecondary;
  static Color get textColor3 => textHint;
  static Color get backgroundColor => backgroundPrimary;
  static Color get backgroundColor2 => backgroundSecondary;
  static Color get backgroundColor3 => surfaceElevated;
  static Color get cardColor => surface;
  static Color get secondaryColor => secondary;
  static Color get errorColor => error;
  static Color get successColor => success;
  static Color get warningColor => warning;
  static Color get infoColor => info;
  static Color get accentColor => accent;
  static Color get surfaceColor => surface;
  static Color get secondaryTextColor => textSecondary;

  /// ألوان النظام القديم
  static Color get colorSW => primary;
  static Color get colorBT => textPrimary;
  static Color get colorBT2 => textSecondary;
  static Color get colorBT3 => textHint;
  static Color get colorBG => backgroundPrimary;
  static Color get colorBG2 => backgroundSecondary;
  static Color get colorBG3 => surfaceElevated;
  static Color get colorAmber => warning;
  static Color get colorRed => error;
  static Color get colorNT1 => textPrimary;
  static Color get colorNT2 => textSecondary;
  static Color get colorblu => secondary;
  static Color get colorred => error;
  static Color get colorgreen => success;
  static Color get colororange => warning;

  /// خاصية current للتوافق
  static AppColorsCurrent get current => AppColorsCurrent();
}

/// فئة التوافق مع النظام القديم - محدثة مع الألوان الجديدة
class AppColorsCurrent {
  /// النص على السطح
  Color get onSurface => AppColors.textPrimary;

  /// النص على السطح المتغير
  Color get onSurfaceVariant => AppColors.textSecondary;

  /// السطح المتغير
  Color get surfaceVariant => AppColors.surfaceElevated;

  /// الخلفية المتغيرة
  Color get backgroundVariant => AppColors.backgroundSecondary;

  /// النص على الأساسي
  Color get onPrimary => AppColors.white;

  /// النص على الثانوي
  Color get onSecondary => AppColors.white;

  /// النص على الخطأ
  Color get onError => AppColors.white;

  /// الألوان الأساسية
  Color get primary => AppColors.primary;
  Color get secondary => AppColors.secondary;
  Color get background => AppColors.backgroundPrimary;
  Color get surface => AppColors.surface;
  Color get error => AppColors.error;
  Color get success => AppColors.success;
  Color get warning => AppColors.warning;
  Color get info => AppColors.info;

  /// الحدود والظلال
  Color get outline => AppColors.border;
  Color get shadow => AppColors.shadow;

  /// ألوان إضافية للتوافق
  Color get primaryVariant => AppColors.primaryDark;
  Color get secondaryVariant => AppColors.accent;
  Color get surfaceContainer => AppColors.surfaceElevated;
  Color get onBackground => AppColors.textPrimary;
  Color get onSurfaceContainer => AppColors.textPrimary;
  Color get outlineVariant => AppColors.borderLight;
  Color get scrim => AppColors.overlay;
}
