import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

bool isScheduler = false;

bool isAM = false;

int h = DateTime.now().hour;
int m = DateTime.now().minute;
List days = [];
bool re = false;
bool allday = false;

alarm(
    {required String txt,
    required context,
    required setState1,
    required Function submit,
    Function? del}) async {
  if (isScheduler == false) {
    isAM = false;
    h = await DateTime.now().hour;
    if (h > 12) {
      h = h - 12;
      isAM = false;
    } else {
      isAM = true;
    }
    m = await DateTime.now().minute;
    days = [];
    re = false;
    allday = false;
  }
  await AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.backgroundColor3,
      width: controller.sizedWidth,
      body: GetBuilder<HomeController>(
          builder: (controller) => StatefulBuilder(
                builder: (context, setState1) => Center(
                    child: Column(children: [
                  Text(
                    'إضافة موعد الى $txt',
                    style: TextStyle(
                        color: AppColors.textColor2,
                        fontSize: controller.sized * 0.015,
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                          height: controller.sizedHight * 0.25,
                          width: controller.sizedWidth * 0.25,
                          child: CupertinoPicker(
                              squeeze: 0.9,
                              looping: true,
                              useMagnifier: true,
                              magnification: 1.8,
                              scrollController: FixedExtentScrollController(
                                  initialItem: h - 1),
                              itemExtent: controller.sizedHight *
                                  0.05, //height of each item

                              backgroundColor: Colors.transparent,
                              onSelectedItemChanged: (hour) {
                                setState1(() {
                                  h = hour + 1;
                                });
                              },
                              children: [
                                for (int i = 1; i < 13; i++)
                                  Center(
                                    child: Text(
                                      i.toString(),
                                      style: TextStyle(
                                          color: h == i
                                              ? Colors.amber
                                              : AppColors.textColor.withOpacity(0.7),
                                          fontSize: controller.sized * 0.025,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  )
                              ])),
                      txtStyle(
                        txt: ':',
                      ),
                      SizedBox(
                          height: controller.sizedHight * 0.25,
                          width: controller.sizedWidth * 0.25,
                          child: CupertinoPicker(
                              squeeze: 0.9,
                              looping: true,
                              useMagnifier: true,
                              magnification: 1.8,
                              scrollController:
                                  FixedExtentScrollController(initialItem: m),
                              itemExtent: controller.sizedHight *
                                  0.05, //height of each item

                              backgroundColor: Colors.transparent,
                              onSelectedItemChanged: (minute) {
                                setState1(() {
                                  m = minute;
                                });
                              },
                              children: [
                                for (var i = 0; i < 60; i++)
                                  Center(
                                      child: Text(
                                    i < 10 ? '0' + i.toString() : i.toString(),
                                    style: TextStyle(
                                        color: m == i
                                            ? Colors.amber
                                            : AppColors.textColor.withOpacity(0.7),
                                        fontSize: controller.sized * 0.025,
                                        fontWeight: FontWeight.bold),
                                  ))
                              ])),
                      Column(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: isAM
                                  ? AppColors.warningColor
                                  : AppColors.textColor2.withOpacity(0.07),
                              borderRadius: BorderRadius.all(
                                  Radius.circular(controller.sized * 0.0075)),
                            ),
                            child: TextButton(
                              onPressed: () {
                                setState1(
                                  () {
                                    isAM = true;
                                  },
                                );
                              },
                              child: txtStyle(
                                txt: 'صباحاً',
                                align: TextAlign.center,
                                color: isAM ? AppColors.textColor : AppColors.textColor3,
                                size: controller.sized * 0.0125,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: controller.sizedHight * 0.02,
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: isAM == false
                                  ? AppColors.warningColor
                                  : AppColors.textColor2.withOpacity(0.07),
                              borderRadius: BorderRadius.all(
                                  Radius.circular(controller.sized * 0.0075)),
                            ),
                            child: TextButton(
                              onPressed: () {
                                setState1(
                                  () {
                                    isAM = false;
                                  },
                                );
                              },
                              child: txtStyle(
                                txt: 'مسائاً',
                                align: TextAlign.center,
                                color: !isAM ? AppColors.textColor : AppColors.textColor3,
                                size: controller.sized * 0.0125,
                              ),
                            ),
                          )
                        ],
                      )
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            radius: controller.sized * 0.022,
                            backgroundColor: days.contains('سبت')
                                ? AppColors.warningColor
                                : AppColors.textColor2.withOpacity(0.07),
                            child: Center(
                              child: TextButton(
                                  onPressed: () async {
                                    setState1(() {
                                      if (days.contains('سبت')) {
                                        days.remove('سبت');
                                        allday = false;
                                      } else {
                                        days.add('سبت');
                                        if (days.length == 7) {
                                          allday = true;
                                        }
                                      }
                                    });
                                    print(h);
                                  },
                                  child: txtStyle(
                                    txt: 'سبـت',
                                    align: TextAlign.center,
                                    color: days.contains('سبت')
                                        ? Colors.white
                                        : AppColors.textColor3,
                                    size: controller.sized * 0.011,
                                  )),
                            ),
                          ),
                          SizedBox(width: controller.sizedWidth * 0.02),
                          CircleAvatar(
                            radius: controller.sized * 0.022,
                            backgroundColor: days.contains('أحد')
                                ? AppColors.warningColor
                                : AppColors.textColor2.withOpacity(0.07),
                            child: Center(
                              child: TextButton(
                                  onPressed: () {
                                    setState1(() {
                                      if (days.contains('أحد')) {
                                        days.remove('أحد');
                                        allday = false;
                                      } else {
                                        days.add('أحد');
                                        if (days.length == 7) {
                                          allday = true;
                                        }
                                      }
                                    });
                                    print(h);
                                  },
                                  child: txtStyle(
                                    txt: 'أحد',
                                    align: TextAlign.center,
                                    color: days.contains('أحد')
                                        ? Colors.white
                                        : AppColors.textColor3,
                                    size: controller.sized * 0.011,
                                  )),
                            ),
                          ),
                          SizedBox(width: controller.sizedWidth * 0.02),
                          CircleAvatar(
                            radius: controller.sized * 0.022,
                            backgroundColor: days.contains('إثنين')
                                ? AppColors.warningColor
                                : AppColors.textColor2.withOpacity(0.07),
                            child: Center(
                              child: TextButton(
                                  onPressed: () {
                                    setState1(() {
                                      if (days.contains('إثنين')) {
                                        days.remove('إثنين');
                                        allday = false;
                                      } else {
                                        days.add('إثنين');
                                        if (days.length == 7) {
                                          allday = true;
                                        }
                                      }
                                    });
                                    print(h);
                                  },
                                  child: txtStyle(
                                    txt: 'إثنين',
                                    align: TextAlign.center,
                                    color: days.contains('إثنين')
                                        ? Colors.white
                                        : AppColors.textColor3,
                                    size: controller.sized * 0.011,
                                  )),
                            ),
                          ),
                          SizedBox(width: controller.sizedWidth * 0.02),
                          CircleAvatar(
                            radius: controller.sized * 0.0222,
                            backgroundColor: days.contains('ثلاثاء')
                                ? AppColors.warningColor
                                : AppColors.textColor2.withOpacity(0.07),
                            child: Center(
                              child: TextButton(
                                  onPressed: () {
                                    setState1(() {
                                      if (days.contains('ثلاثاء')) {
                                        days.remove('ثلاثاء');
                                        allday = false;
                                      } else {
                                        days.add('ثلاثاء');
                                        if (days.length == 7) {
                                          allday = true;
                                        }
                                      }
                                    });
                                    print(h);
                                  },
                                  child: txtStyle(
                                    txt: 'ثلاثاء',
                                    align: TextAlign.center,
                                    color: days.contains('ثلاثاء')
                                        ? Colors.white
                                        : AppColors.textColor3,
                                    size: controller.sized * 0.011,
                                  )),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            radius: controller.sized * 0.022,
                            backgroundColor: days.contains('اربعاء')
                                ? AppColors.warningColor
                                : AppColors.textColor2.withOpacity(0.07),
                            child: Center(
                              child: TextButton(
                                  onPressed: () {
                                    setState1(() {
                                      if (days.contains('اربعاء')) {
                                        days.remove('اربعاء');
                                        allday = false;
                                      } else {
                                        days.add('اربعاء');
                                        if (days.length == 7) {
                                          allday = true;
                                        }
                                      }
                                    });
                                    print(h);
                                  },
                                  child: txtStyle(
                                    txt: 'اربعاء',
                                    align: TextAlign.center,
                                    color: days.contains('اربعاء')
                                        ? Colors.white
                                        : AppColors.textColor3,
                                    size: controller.sized * 0.01,
                                  )),
                            ),
                          ),
                          SizedBox(width: controller.sizedWidth * 0.02),
                          CircleAvatar(
                            radius: controller.sized * 0.0222,
                            backgroundColor: days.contains('خميس')
                                ? AppColors.warningColor
                                : AppColors.textColor2.withOpacity(0.07),
                            child: Center(
                              child: TextButton(
                                  onPressed: () {
                                    setState1(() {
                                      if (days.contains('خميس')) {
                                        days.remove('خميس');
                                        allday = false;
                                      } else {
                                        days.add('خميس');
                                        if (days.length == 7) {
                                          allday = true;
                                        }
                                      }
                                    });
                                    print(h);
                                  },
                                  child: txtStyle(
                                    txt: 'خميس',
                                    align: TextAlign.center,
                                    color: days.contains('خميس')
                                        ? Colors.white
                                        : AppColors.textColor3,
                                    size: controller.sized * 0.0097,
                                  )),
                            ),
                          ),
                          SizedBox(width: controller.sizedWidth * 0.02),
                          CircleAvatar(
                            radius: controller.sized * 0.022,
                            backgroundColor: days.contains('جمعة')
                                ? AppColors.warningColor
                                : AppColors.textColor2.withOpacity(0.07),
                            child: Center(
                              child: TextButton(
                                  onPressed: () {
                                    setState1(() {
                                      if (days.contains('جمعة')) {
                                        days.remove('جمعة');
                                        allday = false;
                                      } else {
                                        days.add('جمعة');
                                        if (days.length == 7) {
                                          allday = true;
                                        }
                                      }
                                    });
                                    print(h);
                                  },
                                  child: txtStyle(
                                    txt: 'جمعة',
                                    align: TextAlign.center,
                                    color: days.contains('جمعة')
                                        ? Colors.white
                                        : AppColors.textColor3,
                                    size: controller.sized * 0.011,
                                  )),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      containerIconsOption(
                        padding: EdgeInsets.only(
                            right: controller.sizedWidth * 0.02),
                        // margin:
                        //     EdgeInsets.only(top: controller.sizedHight * 0.015),
                        content: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            IconButton(
                              onPressed: () {
                                if (days.isEmpty) {
                                  days.addAll([
                                    'سبت',
                                    'أحد',
                                    'إثنين',
                                    'ثلاثاء',
                                    'اربعاء',
                                    'خميس',
                                    'جمعة',
                                  ]);
                                  allday = !allday;
                                }
                                setState1(() {
                                  re = !re;
                                });
                              },
                              icon: re
                                  ? iconStyle(
                                      icon: Icons.check_box_rounded,
                                      color: re
                                          ? AppColors.primaryColor
                                          : AppColors.backgroundColor2.withOpacity(0.7),
                                    )
                                  : iconStyle(
                                      icon:
                                          Icons.check_box_outline_blank_rounded,
                                      color: re ? AppColors.primaryColor : AppColors.backgroundColor2,
                                    ),
                            ),
                            SizedBox(
                              width: controller.sizedWidth * 0.02,
                            ),
                            txtStyle(
                              txt: 'التكرار',
                              color: AppColors.textColor3,
                              size: controller.sized * 0.013,
                            )
                          ],
                        ),
                      ),
                      SizedBox(width: controller.sized * 0.01),
                      containerIconsOption(
                        padding: EdgeInsets.only(
                            right: controller.sizedWidth * 0.02),
                        // margin:
                        //     EdgeInsets.only(top: controller.sizedHight * 0.015),

                        content: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            IconButton(
                              onPressed: () {
                                days.clear();
                                if (!allday) {
                                  days.addAll([
                                    'سبت',
                                    'أحد',
                                    'إثنين',
                                    'ثلاثاء',
                                    'اربعاء',
                                    'خميس',
                                    'جمعة',
                                  ]);
                                }
                                setState1(() {
                                  allday = !allday;
                                  days;
                                });
                              },
                              icon: allday
                                  ? iconStyle(
                                      icon: Icons.check_box_rounded,
                                      color: allday ? AppColors.primaryColor : AppColors.textColor3,
                                    )
                                  : iconStyle(
                                      icon:
                                          Icons.check_box_outline_blank_rounded,
                                      color: allday ? AppColors.primaryColor : AppColors.backgroundColor2,
                                    ),
                            ),
                            SizedBox(
                              width: controller.sizedWidth * 0.02,
                            ),
                            Text(
                              'كل الايام',
                              style: TextStyle(
                                  color: AppColors.textColor3,
                                  fontSize: controller.sized * 0.013,
                                  fontWeight: FontWeight.bold),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.04,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      isScheduler
                          ? delButtom(
                              onPressed: () async {
                                if (Navigator.of(context).canPop()) {
                                  Navigator.of(context).pop();
                                }
                                del!();
                              },
                            )
                          : Container(),
                      isScheduler
                          ? SizedBox(width: controller.sizedWidth * 0.01)
                          : Container(),
                      submitButtom(
                        text: isScheduler ? 'تعديل' : 'حفظ',
                        onPressed: () async {
                          if (Navigator.of(context).canPop()) {
                            Navigator.of(context).pop();
                          }
                          submit();
                          // setState1(
                          //   () {
                          //     isScheduler = true;
                          //   },
                          // );
                        },
                      ),
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                ])),
              ))).show();
}
