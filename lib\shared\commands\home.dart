import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/themes/app_colors.dart';

HomeController controller = Get.find();
commandHome(bool command) {
  if (client.connectionStatus!.state.name == 'connected') {
    controller.homeState = command;
    for (var i in controller.rooms.values) {
      for (var e in i['devices'].values) {
        if (controller.rooms[i['id']]['devices'][e['id']]['device']
                    .contains('ZAIN') !=
                true &&
            controller.devices[e['id']]) {
          final builder = MqttClientPayloadBuilder();
          if (controller.rooms[i['id']]['devices'][e['id']]['state'] == null) {
            continue;
          }
          controller.rooms[i['id']]['state'] = command;
          bool state = controller.rooms[i['id']]['devices'][e['id']]['state'];

          controller.rooms[i['id']]['devices'][e['id']]['state'] = command;
          if (controller.rooms[i['id']]['devices'][e['id']]['device'] == 'AC') {
            if (command == true && state != true) {
              builder.addString(controller.rooms[i['id']]['devices'][e['id']]
                      ['id'] +
                  ' AC RUN ' +
                  controller.rooms[i['id']]['devices'][e['id']]['degree']
                      .toInt()
                      .toString() +
                  ' VAN ' +
                  controller.rooms[i['id']]['devices'][e['id']]['speed']
                      .toString() +
                  ' ' +
                  (controller.rooms[i['id']]['devices'][e['id']]['type']
                              .toString() ==
                          'تبريد'
                      ? 'AC'
                      : controller.rooms[i['id']]['devices'][e['id']]['type']
                                  .toString() ==
                              'تدفئه'
                          ? 'HEAT'
                          : 'VAN'));
              client.publishMessage(controller.homeId + "/app/zain",
                  MqttQos.atLeastOnce, builder.payload!);
            } else if (command == false && state != false) {
              builder.addString(controller.rooms[i['id']]['devices'][e['id']]
                      ['id'] +
                  ' AC OFF');
              client.publishMessage(controller.homeId + "/app/zain",
                  MqttQos.atLeastOnce, builder.payload!);
            }
          } else if (controller.rooms[i['id']]['devices'][e['id']]['device'] ==
              'TV') {
            print('8888888888888888888888888888888888888888888888888888888888');
            print(command);
            print(state);
            if (command == true && state != true) {
              builder.addString(controller.rooms[i['id']]['devices'][e['id']]
                      ['id'] +
                  ' TV POWER-ON');
              client.publishMessage(controller.homeId + "/app/zain",
                  MqttQos.atLeastOnce, builder.payload!);
            } else if (command == false && state != false) {
              builder.addString(controller.rooms[i['id']]['devices'][e['id']]
                      ['id'] +
                  ' TV POWER-OFF');
              client.publishMessage(controller.homeId + "/app/zain",
                  MqttQos.atLeastOnce, builder.payload!);
            }
          } else if (controller.rooms[i['id']]['devices'][e['id']]['device'] ==
              'SWITCH') {
            String pubMassege =
                controller.rooms[i['id']]['devices'][e['id']]['id'] + ' SWITCH';
            for (var s in controller.rooms[i['id']]['devices'][e['id']].keys
                .toList()) {
              if (s != 'id' &&
                  s != 'device' &&
                  s != 'state' &&
                  s != 'pub' &&
                  s != 'priv' &&
                  s != 'pubName' &&
                  s != 'privName') {
                if (command == true) {
                  pubMassege += ' ' + s + '_RUN';
                } else {
                  pubMassege += ' ' + s + '_OFF';
                }
                controller.rooms[i['id']]['devices'][e['id']][s]['state'] =
                    command;
              }
            }
            builder.addString(pubMassege);
            client.publishMessage(controller.homeId + "/app/zain",
                MqttQos.atLeastOnce, builder.payload!);
          }
        }
      }
    }

    // if (i['device'] == 'SWITCH') {
    //   for (var j in i.keys.toList()) {
    //     if (j != 'id' &&
    //         j != 'device' &&
    //         j != 'state' &&
    //         j != 'pub' &&
    //         j != 'priv' &&
    //         j != 'pubName' &&
    //         j != 'privName') {
    //       rooms[rooms.indexWhere((element) =>
    //               element['id'] == i['id'])]
    //           [j]['state'] = val;
    //     }
    //   }
    // } else {}

    controller.update();
  }
  // });
}
