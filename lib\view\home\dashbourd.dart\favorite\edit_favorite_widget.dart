import 'package:flutter/material.dart';
import 'package:reorderable_grid_view/reorderable_grid_view.dart';
import 'package:zaen/shared/components/components.dart';
import '../../../../shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget EditFavorite() {
  return StatefulBuilder(builder: ((context, setState) {
    return Center(
      child: Container(
          height: controller.sizedHight * 0.38,
          margin: EdgeInsets.fromLTRB(
            controller.sizedWidth * 0.04,
            0,
            controller.sizedWidth * 0.04,
            0,
          ),
          child: ReorderableGridView.count(
            dragWidgetBuilderV2: DragWidgetBuilderV2(
                isScreenshotDragWidget: true,
                builder: (index, child, screenshot) {
                  return child;
                }),
            crossAxisCount: 2,
            mainAxisSpacing: controller.sized * 0.007,
            crossAxisSpacing: controller.sized * 0.0275,
            padding: EdgeInsets.only(top: controller.sizedHight * 0.007),
            children: controller.favorite.map((item) {
              var room = '';
              for (var r in controller.rooms.keys) {
                if (controller.rooms[r]['devices'].keys.contains(item)) {
                  room = r;
                }
              }
              return _buildEditFavoriteItem(item, room);
            }).toList(),
            onReorder: (oldIndex, newIndex) {
              // Debug prints removed for production
              setState(() {
                final item = controller.favorite.removeAt(oldIndex);
                controller.favorite.insert(newIndex, item);
              });
            },
          )),
    );
  }));
}

Widget _buildEditFavoriteItem(String item, String room) {
  return Container(
    key: ValueKey(item),
    width: controller.sizedWidth * 0.4, // متناسق مع AnimatedContainer
    height: controller.sizedHight * 0.18, // متناسق مع AnimatedContainer
    margin: EdgeInsets.only(bottom: controller.sizedHight * 0.02),
    decoration: BoxDecoration(
      borderRadius: const BorderRadius.all(Radius.circular(30)),
      color: AppColors.backgroundColor3.withOpacity(0.7), // نفس الشفافية
    ),
    child: Card(
      color: Colors.transparent,
      elevation: 0,
      child: Center(
        child: Padding(
          padding: EdgeInsets.only(right: controller.sizedWidth * 0.01),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: controller.sizedHight * 0.005,
                    bottom: controller.sizedHight * 0.009,
                    left: controller.sizedWidth * 0.009,
                  ),
                  child: Row(
                    textDirection: TextDirection.rtl,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      iconStyle(
                        color: AppColors.warningColor.withOpacity(0.7),
                        icon: _getEditDeviceIcon(
                            controller.rooms[room]['devices'][item]['device']),
                      ),
                      iconStyle(
                        icon: Icons.menu,
                        color: AppColors.backgroundColor3.withOpacity(0.5),
                      ),
                    ],
                  ),
                ),
                Row(
                  textDirection: TextDirection.rtl,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    txtStyle(txt: controller.rooms[room]['privName']),
                  ],
                ),
                SizedBox(height: controller.sizedHight * 0.02),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: txtStyle(
                          txt: _getEditDeviceDisplayName(
                              controller.rooms[room]['devices'][item]),
                          maxLines: 2,
                          align: TextAlign.start),
                    ),
                  ],
                ),
                SizedBox(height: controller.sizedHight * 0.03),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}

IconData _getEditDeviceIcon(String deviceType) {
  if (deviceType.contains('ZAIN')) return Icons.flutter_dash_rounded;
  if (deviceType.contains('TV')) return Icons.tv_rounded;
  if (deviceType.contains('SW')) return Icons.power_outlined;
  if (deviceType.contains('AC')) return Icons.ac_unit_rounded;
  return Icons.device_unknown;
}

String _getEditDeviceDisplayName(Map device) {
  if (device['device'].contains('ZAIN')) {
    return device['pubName'];
  } else if (device['device'].contains('SW')) {
    return device['priv'].split('_')[0];
  } else {
    return device['priv'];
  }
}
