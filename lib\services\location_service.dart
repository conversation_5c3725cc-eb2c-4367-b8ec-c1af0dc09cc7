import 'package:geolocator/geolocator.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Position? currentPosition;
String locationMessage = "احصل على الموقع";

Future<void> getCurrentLocation() async {
  // التحقق من الصلاحيات
  bool serviceEnabled;
  LocationPermission permission;

  // تحقق من تشغيل خدمات الموقع
  serviceEnabled = await Geolocator.isLocationServiceEnabled();
  if (!serviceEnabled) {
    print('خدمات الموقع متوقفة');
    return;
  }

  // طلب الصلاحيات
  permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.denied) {
      print('تم رفض إذن الموقع');
      return;
    }
  }

  if (permission == LocationPermission.deniedForever) {
    print('صلاحيات الموقع مرفوضة نهائياً');
    return;
  }

  // احصل على الموقع
  try {
    print('ooooooooooo');
    Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    print('خط الطول: ${position.longitude}\n' +
        'خط العرض: ${position.latitude}\n' +
        'الدقة: ${position.accuracy}');
    currentPosition = position;
  } catch (e) {
    print('خطأ في جلب الموقع: $e');
  }
}
