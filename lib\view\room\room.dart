import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/cupertino.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:image_picker/image_picker.dart';
import 'package:zaen/controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/commands/room.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/models/pages.dart';
import 'package:zaen/view/room/del.dart';
import 'package:zaen/view/room/double_tap/edit_names.dart';
import 'package:zaen/view/room/edit_names.dart';
import 'package:zaen/view/room/shortcut/ac.dart';
import 'package:zaen/view/room/shortcut/sw.dart';
import 'package:zaen/view/room/shortcut/tv.dart';
import 'package:zaen/view/room/shortcut/zain.dart';
import 'package:zaen/shared/themes/app_colors.dart';

class Room extends StatefulWidget {
  @override
  State<Room> createState() => _RoomState();
}

class _RoomState extends State<Room> {
  HomeController controller = Get.find();

  @override
  void initState() {
    super.initState();
    getRoomDevices();
  }

  @override
  Widget build(BuildContext context) {
    print(room);
    return GetBuilder<HomeController>(
        builder: (controller) => Scaffold(
            extendBody: true,
            body: Stack(children: [
              roomImage.contains('com.example.zaen')
                  ? Image.file(
                      File(roomImage),
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      color: AppColors.textSecondary.withOpacity(0.3),
                      colorBlendMode: BlendMode.lighten,
                      fit: BoxFit.cover,
                    )
                  : Image.asset(
                      "$roomImage",
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      color: AppColors.textSecondary.withOpacity(0.3),
                      colorBlendMode: BlendMode.lighten,
                      fit: BoxFit.cover,
                    ),
              BackdropFilter(
                blendMode: BlendMode.srcIn,
                filter: ImageFilter.blur(
                  sigmaX: 40,
                  sigmaY: 40,
                ),
                child: Container(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    color: AppColors.textColor.withOpacity(0.1)),
              ),
              NestedScrollView(
                  headerSliverBuilder:
                      (BuildContext context, bool innerBoxIsScrolled) {
                    return <Widget>[
                      Directionality(
                        textDirection: TextDirection.rtl,
                        child: SliverAppBar(
                          expandedHeight: controller.sizedHight * 0.2,
                          automaticallyImplyLeading: false,
                          backgroundColor:
                              AppColors.backgroundColor.withOpacity(0.65),
                          // leadingWidth: double.infinity,
                          actions: [
                            IconButton(
                              icon: const Icon(Icons.settings_rounded),
                              onPressed: () {
                                showBottomSheet(
                                    enableDrag: true,
                                    backgroundColor: Colors.transparent,
                                    context: context,
                                    builder: (context) {
                                      return GetBuilder<HomeController>(
                                          builder: (controller) => RoomPage(
                                              roomPrivName: room,
                                              image: roomImage,
                                              homeType: homeType,
                                              asset: () async {
                                                final manifestContent =
                                                    await rootBundle.loadString(
                                                        'AssetManifest.json');

                                                Map<String, dynamic>
                                                    manifestMap =
                                                    await json.decode(
                                                        manifestContent);
                                                // >> To get paths you need these 2 lines

                                                List imagePaths = await manifestMap
                                                    .keys
                                                    .where((String key) =>
                                                        key.contains(
                                                            'images/places/$homeType/'))
                                                    .toList();
                                                print(imagePaths);
                                                var item = imagePaths
                                                    .indexOf(roomImage);
                                                if (item == -1) {
                                                  item = imagePaths.length;
                                                }
                                                print(item);

                                                await showCupertinoModalPopup(
                                                    context: context,
                                                    barrierColor: AppColors
                                                        .textColor2
                                                        .withOpacity(0.35),
                                                    builder: (builder) {
                                                      return StatefulBuilder(
                                                          builder: ((context,
                                                                  setState) =>
                                                              Center(
                                                                child:
                                                                    Container(
                                                                        width: controller
                                                                            .sizedWidth,
                                                                        height: controller.sizedHight *
                                                                            0.3,
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          color: AppColors
                                                                              .backgroundColor2
                                                                              .withOpacity(0.975),
                                                                        ),
                                                                        child:
                                                                            Column(
                                                                          mainAxisSize:
                                                                              MainAxisSize.min,
                                                                          crossAxisAlignment:
                                                                              CrossAxisAlignment.center,
                                                                          mainAxisAlignment:
                                                                              MainAxisAlignment.start,
                                                                          children: [
                                                                            Expanded(
                                                                              child: RotatedBox(
                                                                                quarterTurns: 1,
                                                                                child: Center(
                                                                                  child: ListWheelScrollView(
                                                                                      magnification: 5,
                                                                                      // useMagnifier: true,
                                                                                      controller: FixedExtentScrollController(initialItem: item),
                                                                                      // magnification: 2.0,

                                                                                      itemExtent: controller.sizedWidth * 0.55,
                                                                                      diameterRatio: 20,
                                                                                      squeeze: 0.88,
                                                                                      // overAndUnderCenterOpacity: 0.5,
                                                                                      // scrollDirection:
                                                                                      //     Axis.horizontal,
                                                                                      // reverse: true,
                                                                                      onSelectedItemChanged: (x) {
                                                                                        print(x);
                                                                                        setState(() {
                                                                                          item = x;
                                                                                        });
                                                                                      },
                                                                                      children: List.generate(
                                                                                        imagePaths.length,
                                                                                        (index) => RotatedBox(
                                                                                          quarterTurns: -1,
                                                                                          child: Center(
                                                                                            child: AnimatedContainer(
                                                                                              duration: Duration(milliseconds: 450),
                                                                                              width: item == index ? controller.sizedWidth * 0.7 : controller.sizedWidth * 0.5,
                                                                                              height: item == index ? controller.sizedHight * 0.16 : controller.sizedHight * 0.13,
                                                                                              decoration: BoxDecoration(
                                                                                                  image: DecorationImage(
                                                                                                image: AssetImage(imagePaths[index].toString()),
                                                                                                onError: (context, stackTrace) {},
                                                                                                colorFilter: ColorFilter.mode(
                                                                                                  item == index ? AppColors.textColor.withOpacity(0.1) : AppColors.subtitleColor.withOpacity(0.6),
                                                                                                  BlendMode.darken,
                                                                                                ),
                                                                                                fit: BoxFit.cover,
                                                                                                filterQuality: FilterQuality.high,
                                                                                              )),

                                                                                              //   child:Image.asset(
                                                                                              //   imagePaths[index].toString(),
                                                                                              //   errorBuilder: (context, error, stackTrace) {
                                                                                              //     return Container();
                                                                                              //   },

                                                                                              //   colorBlendMode: BlendMode.darken,
                                                                                              //   fit: BoxFit.cover,
                                                                                              //   filterQuality: FilterQuality.high,
                                                                                              // )
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      )),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                            submitButtom(
                                                                              onPressed: () async {
                                                                                roomImage = imagePaths[item];
                                                                                var appDB = await openDatabase('${controller.system}.db', version: 3);
                                                                                await appDB.rawQuery("UPDATE rooms set image='${imagePaths[item]}' WHERE id = '${roomId}'");
                                                                                controller.rooms[roomId]['image'] = imagePaths[item];

                                                                                controller.update();
                                                                              },
                                                                            ),
                                                                            SizedBox(
                                                                              height: controller.sizedHight * 0.01,
                                                                            )
                                                                          ],
                                                                        )),
                                                              )));
                                                    });
                                              },
                                              roll: () async {
                                                XFile? image =
                                                    await ImagePicker()
                                                        .pickImage(
                                                            source: ImageSource
                                                                .gallery);
                                                if (image == null) {
                                                  print(
                                                      '555555555555555555555555');
                                                  return;
                                                }
                                                final imageTemp =
                                                    File(image.path);
                                                print(imageTemp.path);
                                                roomImage = imageTemp.path;
                                                var appDB = await openDatabase(
                                                    '${controller.system}.db',
                                                    version: 3);
                                                await appDB.rawQuery(
                                                    'UPDATE rooms set image="${imageTemp.path}" WHERE id = "${roomId}"');
                                                getDevices();
                                              },
                                              camera: () async {
                                                XFile? image =
                                                    await ImagePicker()
                                                        .pickImage(
                                                            source: ImageSource
                                                                .camera);
                                                if (image == null) {
                                                  print(
                                                      '555555555555555555555555');
                                                  return;
                                                }
                                                final imageTemp =
                                                    File(image.path);
                                                print(imageTemp.path);
                                                roomImage = imageTemp.path;
                                                var appDB = await openDatabase(
                                                    '${controller.system}.db',
                                                    version: 3);
                                                await appDB.rawQuery(
                                                    'UPDATE rooms set image="${imageTemp.path}" WHERE id = "${roomId}"');
                                                getDevices();
                                              },
                                              del: () async {
                                                final conn =
                                                    await MySqlConnection
                                                        .connect(
                                                            ConnectionSettings(
                                                                host: controller
                                                                    .hostZain
                                                                    .value,
                                                                // port: 80,
                                                                user: 'root',
                                                                db: 'zain',
                                                                password:
                                                                    'zain',
                                                                characterSet:
                                                                    CharacterSet
                                                                        .UTF8));
                                                var appDB = await openDatabase(
                                                    '${controller.system}.db',
                                                    version: 3);
                                                del(
                                                    context: context,
                                                    conn: conn,
                                                    appDB: appDB,
                                                    roomId: roomId,
                                                    room: room);
                                              },
                                              editNames: (string) {
                                                editRoomNames(
                                                    context: context,
                                                    roomId: roomId,
                                                    string: string);
                                              },
                                              editPrivName: (privN, priv) {
                                                print(privN!);
                                                if (privN) {
                                                  print(123);
                                                  void s() async {
                                                    var appDB = await openDatabase(
                                                        '${controller.system}.db',
                                                        version: 3);
                                                    print(1234);
                                                    var myHome =
                                                        await appDB.rawQuery(
                                                            'SELECT * FROM rooms');

                                                    print(myHome);

                                                    await appDB.transaction(
                                                        (txn) async {
                                                      print(12345);
                                                      await txn.rawUpdate(
                                                          'UPDATE rooms SET name = ? WHERE id = ?',
                                                          [priv, roomId]);
                                                      print(123456);
                                                    });

                                                    myHome = await appDB.rawQuery(
                                                        'SELECT * FROM rooms');
                                                    print(myHome);
                                                    await appDB.close();
                                                  }

                                                  s();
                                                  controller.rooms[roomId]
                                                      ['privName'] = priv;
                                                  // i['priv']=priv;
                                                  privN = false;
                                                } else {
                                                  priv =
                                                      room != null ? room : 'X';
                                                }
                                                room = priv;
                                                controller.update();
                                              },
                                              sizedWidth: controller.sizedWidth,
                                              sizedHeight:
                                                  controller.sizedHight,
                                              sized: controller.sized));
                                    });
                              },
                              color: AppColors.textColor.withOpacity(0.9),
                              iconSize: controller.sized * 0.025,
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: controller.sizedWidth * 0.02),
                              child: controller.devices[roomId] == false ||
                                      client.connectionStatus!.state.name !=
                                          'connected'
                                  ? Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal:
                                              controller.sizedWidth * 0.01,
                                          vertical:
                                              controller.sizedHight * 0.015),
                                      child: Text(
                                        'غير متصل',
                                        textDirection: TextDirection.rtl,
                                        style: TextStyle(
                                            color:
                                                Color.fromARGB(255, 238, 19, 3),
                                            fontSize: controller.sized * 0.015,
                                            fontWeight: FontWeight.bold),
                                      ))
                                  : switchStyle(
                                      value: controller.rooms[roomId]['state'],
                                      onChanged: (val) {
                                        commandRoom(val!, roomId);
                                      }),
                            ),
                          ],

                          leading: IconButton(
                            icon: Icon(Icons.arrow_back_ios_rounded),
                            onPressed: () {
                              if (Navigator.of(context).canPop()) {
                                Navigator.of(context).pop();
                              }
                            },
                            iconSize: controller.sized * 0.027,
                            color: AppColors.textColor.withOpacity(0.9),
                          ),
                          flexibleSpace: ClipRect(
                            child: BackdropFilter(
                              filter: ImageFilter.blur(
                                sigmaX: 75,
                                sigmaY: 75,
                              ),
                              child: FlexibleSpaceBar(
                                titlePadding: EdgeInsets.symmetric(
                                    vertical: controller.sizedHight * 0.015),
                                centerTitle: true,
                                expandedTitleScale: controller.sized * 0.0015,
                                title: Container(
                                  width: controller.sizedWidth * 0.44,
                                  child: FittedBox(
                                    alignment: Alignment.center,
                                    fit: BoxFit.scaleDown,
                                    child: txtStyle(
                                      txt: room != 'x' ? room : 'لا يوجد اسم',
                                      color: AppColors.textColor,
                                      size: controller.sized * 0.018,
                                    ),
                                  ),
                                ),
                                background:
                                    roomImage.contains('com.example.zaen')
                                        ? Image.file(
                                            File(roomImage),
                                            color: AppColors.subtitleColor
                                                .withOpacity(0.3),
                                            colorBlendMode: BlendMode.darken,
                                            fit: BoxFit.cover,
                                            filterQuality: FilterQuality.high,
                                          )
                                        : Image.asset(
                                            "$roomImage",
                                            color: AppColors.subtitleColor
                                                .withOpacity(0.3),
                                            colorBlendMode: BlendMode.darken,
                                            fit: BoxFit.cover,
                                            filterQuality: FilterQuality.high,
                                          ),
                              ),
                            ),
                          ),
                          pinned: true,
                          //Whether the app bar should remain visible at the start of the scroll view
                          floating: false,
                          //Whether the app bar should become visible as soon as the user scrolls towards the app bar.
                          snap: false,
                        ),
                      )
                    ];
                  },
                  body: ConditionalBuilder(
                    condition: controller.home.isNotEmpty &&
                        client.connectionStatus!.state.name == 'connected',
                    fallback: (context) =>
                        const Center(child: CircularProgressIndicator()),
                    builder: (context) => SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      child: Column(
                        children: [
                          SizedBox(
                            height: controller.sizedHight * 0.01,
                          ),
                          for (dynamic i
                              in controller.rooms[roomId]['devices'].values)
                            (i['device'] == 'AC'
                                ? roomAc(context: context, i: i)
                                : i['device'] == 'TV'
                                    ? roomTv(context: context, i: i)
                                    : i['device'] == 'SWITCH'
                                        ? roomSw(context: context, i: i)
                                        : i['device']
                                                .toString()
                                                .contains('ZAIN')
                                            ? roomZain(context: context, i: i)
                                            : Container()),
                          const SizedBox(
                            height: 30,
                          ),
                        ],
                      ),
                    ),
                  )),
            ])));
  }

  void getRoomDevices() {
    room = controller.roomData['privName'];
    homeType = controller.homeType.value;
    // print(widget.arguments.toList()[5][0]);
    roomImage = controller.roomData['image'];
    roomState = controller.roomData['state'];
    roomId = controller.roomData['id'];
    roomdevices = controller.roomData['devices'];
    print(controller.roomData);
  }
}
