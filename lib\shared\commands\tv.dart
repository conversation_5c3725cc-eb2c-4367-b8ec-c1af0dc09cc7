import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

commandTvSw(bool command, Map tv, String roomId) {
  if (client.connectionStatus!.state.name == 'connected') {
    switchTap('state', tv['state'], tv['id']);
    roomState = false;

    for (var j in controller.rooms[roomId]['devices'].values) {
      if (j['state'] == true) {
        roomState = true;
      }
      // setState(() {
      controller.rooms[roomId]['state'] = roomState;
      // });
      if (command == true) {
        controller.homeState = true;
      } else {
        controller.homeState = false;
        for (var i in controller.rooms.values) {
          if (i['state'] == true) {
            controller.homeState = true;
          }
        }
      }
    }
    final builder = MqttClientPayloadBuilder();

    if (command == true) {
      builder.addString(tv['id'] + ' TV POWER-ON');
    } else {
      builder.addString(tv['id'] + ' TV POWER-OFF');
    }
    client.publishMessage(
        controller.homeId + "/app/zain", MqttQos.atLeastOnce, builder.payload!);

    controller.update();
  }
}

commandTvRemote(String command, Map tv, String roomId) {
  if (client.connectionStatus!.state.name == 'connected') {
    // if (controller.rooms[roomId]['devices'][tv['id']]['state'] == false)
    //   switchTap('state', tv['state'], tv['id']);
    if (command.contains('SIL') ||
        (command.contains('VOICE') &&
            controller.rooms[roomId]['devices'][tv['id']]['sil'] == false))
      switchTap('sil', tv['sil'], tv['id']);

    // controller.rooms[roomId]['state'] = true;
    // controller.homeState = true;
    final builder = MqttClientPayloadBuilder();

    builder.addString(tv['id'] + ' TV $command');
    client.publishMessage(
        controller.homeId + "/app/zain", MqttQos.atLeastOnce, builder.payload!);

    controller.update();

    // يتم ارسال زياده الصوت درجه واحده
    // اذا كان التلفاز مغلق يتم اظهار اشعار يوضح انه سيتم فتح التلفاز اولا و بعد 15 ثانيه يزيد الصوت درجه واحد
  }
}
