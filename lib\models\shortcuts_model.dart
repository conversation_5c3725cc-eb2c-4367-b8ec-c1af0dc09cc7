import 'package:expansion_tile_card/expansion_tile_card.dart';
import 'package:toggle_switch/toggle_switch.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import 'dart:math' as math;
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget shortcutAc({
  var deviceState = null,
  bool swingState = false,
  var speedState = 2,
  var typeState = 1,
  var degree = 25.0,
  required bool connect,
  String? acPrivName,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  required Function() doubleTap,
  required Function() tapOn_Ac_Icon,
  required Function() acRun,
  required Function(bool?) switchState,
  required Function(double?) sliderState,
  required Function(int?) acTypeState,
  required Function() acSwingState,
  required Function() acSpeedsStateLeft,
  required Function() acSpeedsStateRight,
  // required var image,
}) =>
    shortCutStyle(
        connect: connect,
        type: 'مكيف',
        doubleTap: doubleTap,
        tapOnIcon: tapOn_Ac_Icon,
        PrivName: acPrivName!,
        content: [
          Padding(
            padding: EdgeInsets.only(left: sizedWidth * 0.015),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Slider(
                      min: 16,
                      max: 30,
                      activeColor: AppColors.textColor.withOpacity(0.7),
                      inactiveColor: AppColors.textColor.withOpacity(0.15),
                      thumbColor: AppColors.textColor.withOpacity(0.95),
                      value: degree.toDouble(),
                      onChanged: sliderState),
                ),
                Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: sizedWidth * 0.01,
                        vertical: sizedHeight * 0.005),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor.withOpacity(0.25),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(
                      '${degree.toInt()}°',
                      style: TextStyle(
                          fontSize: sized * 0.015,
                          color: AppColors.textColor2,
                          fontWeight: FontWeight.bold),
                    )),
              ],
            ),
          ),
          Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                GestureDetector(
                  onTap: acSwingState,
                  child: Container(
                    height: sizedHeight * 0.045,
                    padding:
                        EdgeInsets.symmetric(horizontal: sizedWidth * 0.04),
                    decoration: BoxDecoration(
                        color: swingState
                            ? AppColors.textColor.withOpacity(0.8)
                            : AppColors.backgroundColor.withOpacity(0.25),
                        borderRadius: BorderRadius.circular(5)),
                    child: Center(
                      child: Text(
                        'التأرجح',
                        style: TextStyle(
                          color: swingState
                              ? AppColors.subtitleColor.withOpacity(0.8)
                              : AppColors.textColor3,
                          fontSize: sized * 0.014,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: sizedWidth * 0.025,
                ),
                ToggleSwitch(
                  cornerRadius: 25,
                  curve: Curves.bounceIn,
                  minHeight: sizedHeight * 0.045,
                  minWidth: sizedWidth * 0.2,
                  activeBgColor: [AppColors.primary.withOpacity(0.8)],
                  activeFgColor: AppColors.white,
                  inactiveBgColor: AppColors.surface.withOpacity(0.5),
                  inactiveFgColor: AppColors.textHint,
                  fontSize: sized * 0.015,
                  initialLabelIndex: typeState,
                  labels: const ['تدفئه', 'تبريد', 'مروحه'],
                  onToggle: acTypeState,
                ),
              ]),
          Padding(
            padding: EdgeInsets.only(left: sizedWidth * 0.015),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                      flex: 2,
                      child: Container(
                        padding: EdgeInsets.zero,
                      )),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      txtStyle(
                        txt: 'سرعه المروحه',
                        color: AppColors.textColor3,
                        size: sized * 0.01,
                      ),
                      Container(
                        height: sizedHeight * 0.045,
                        padding: EdgeInsets.zero,
                        decoration: BoxDecoration(
                          color: AppColors.backgroundColor.withOpacity(0.25),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(5)),
                        ),
                        child: Row(
                          children: [
                            GestureDetector(
                              onTap: acSpeedsStateRight,
                              child: Icon(
                                Icons.arrow_left,
                                size: sized * 0.028,
                                color: AppColors.textColor3,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: sizedWidth * 0.075),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.air,
                                    color: AppColors.textColor3,
                                    size: sized * 0.025,
                                  ),
                                  SizedBox(
                                    width: sizedWidth * 0.005,
                                  ),
                                  Text(
                                    acFanSpeed[speedState],
                                    style: TextStyle(
                                        fontSize: sized * 0.012,
                                        color: AppColors.textColor3,
                                        fontWeight: FontWeight.bold),
                                  )
                                ],
                              ),
                            ),
                            GestureDetector(
                              onTap: acSpeedsStateLeft,
                              child: Icon(
                                Icons.arrow_right,
                                size: sized * 0.028,
                                color: AppColors.textColor3,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                      child: Container(
                    padding: EdgeInsets.zero,
                  )),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      SizedBox(
                        height: sized * 0.01,
                      ),
                      Transform.rotate(
                        angle: 180 * math.pi / 180,
                        child: IconButton(
                          padding: EdgeInsets.zero,
                          onPressed: acRun,
                          icon: Icon(
                            Icons.play_circle_fill_rounded,
                            size: sized * 0.05,
                            color: AppColors.primaryColor.withOpacity(0.85),
                          ),
                        ),
                      ),
                    ],
                  ),
                ]),
          ),
          SizedBox(
            height: sizedHeight * 0.01,
          )
        ],
        deviceState: deviceState,
        switchState: switchState);

Widget shortcutTv({
  var deviceState = null,
  required Function() doubleTap,
  required Function(bool?) switchState,
  required Function() tapOn_Tv_Icon,
  required Function() tapOn_VolumeUp,
  required Function() tapOn_VolumeDown,
  required Function() tapOn_ChUp,
  required Function() tapOn_ChDown,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_123,
  required Function() tapOn_menu,
  required Function() tapOn_star,
  required bool connect,
  String? tvPrivName,
  required bool sil,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  // required var image,
}) =>
    shortCutStyle(
        connect: connect,
        type: 'تلفاز',
        doubleTap: doubleTap,
        tapOnIcon: tapOn_Tv_Icon,
        PrivName: tvPrivName!,
        content: [
          SizedBox(
            height: sizedHeight * 0.015,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              Container(
                width: sizedWidth * 0.13,
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor.withOpacity(0.25),
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                ),
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: tapOn_ChUp,
                      child: Icon(Icons.arrow_drop_up,
                          size: sized * 0.03, color: AppColors.textColor3),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: tapOn_menu,
                      iconSize: sized * 0.035,
                      icon: Icon(
                        Icons.swap_horiz_rounded,
                        color: AppColors.textColor2,
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    GestureDetector(
                      onTap: tapOn_ChDown,
                      child: Icon(Icons.arrow_drop_down,
                          size: sized * 0.03, color: AppColors.textColor3),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: sizedWidth * 0.1,
              ),
              Container(
                // padding: EdgeInsets.o,
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor.withOpacity(0.25),
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                ),
                child: Column(
                  children: [
                    SizedBox(
                      height: sizedHeight * 0.006,
                    ),
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: tapOn_star,
                      icon: const Icon(
                        Icons.important_devices_rounded,
                      ),
                      iconSize: sized * 0.035,
                      color: AppColors.textColor2,
                    ),
                    SizedBox(
                      width: sizedWidth * 0.15,
                      height: sized * 0.027,
                    ),
                    IconButton(
                      padding: EdgeInsets.only(bottom: 5),
                      onPressed: tapOn_123,
                      icon: const Icon(Icons.pin_rounded),
                      iconSize: sized * 0.035,
                      color: AppColors.textColor2,
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: sizedWidth * 0.1,
              ),
              Container(
                width: sizedWidth * 0.13,
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor.withOpacity(0.25),
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                ),
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: tapOn_VolumeUp,
                      child: Icon(Icons.arrow_drop_up,
                          size: sized * 0.03, color: AppColors.textColor3),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: tapOn_VolumeMute,
                      iconSize: sized * 0.032,
                      icon: Icon(
                        sil
                            ? Icons.volume_up_rounded
                            : Icons.volume_off_rounded,
                        color: AppColors.textColor2,
                      ),
                    ),
                    SizedBox(
                      height: sizedHeight * 0.015,
                    ),
                    GestureDetector(
                      onTap: tapOn_VolumeDown,
                      child: Icon(Icons.arrow_drop_down,
                          size: sized * 0.03, color: AppColors.textColor3),
                    ),
                  ],
                ),
              ),
              // SizedBox(
              //   width: 20,
              // ),
            ],
          ),
          SizedBox(
            height: 10,
          ),
        ],
        deviceState: deviceState,
        switchState: switchState);

Widget shortcutSwitch({
  var deviceState = null,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  required bool connect,
  String? SwPrivName,
  required Map swList,
  required Function() doubleTap,
  required Function(bool?) switchState,
  required Function(String?, bool?) switchTap,
  // required var image,
}) =>
    shortCutStyle(
        connect: connect,
        type: 'مفاتيح',
        doubleTap: doubleTap,
        tapOnIcon: () {},
        PrivName: SwPrivName!.split('_')[0],
        content: connect
            ? <Widget>[
                Directionality(
                  textDirection: TextDirection.rtl,
                  child: Container(
                    // height: 160,
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: GridView(
                      padding: const EdgeInsets.only(top: 5),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        childAspectRatio: 0.75,
                      ),
                      children: [
                        for (String i in swList.keys
                            .toList()
                            .getRange(0, swList.length - 4))
                          Container(
                              // height: 160,
                              // margin: const EdgeInsets.symmetric(horizontal: 5),
                              // color: AppColors.textColor,
                              child: CircularPercentIndicator(
                                  radius: sized * 0.03,
                                  lineWidth: sized * 0.006,
                                  percent: swList[i]['state'] ? 0.965 : 0.0,
                                  // startAngle: 1,
                                  backgroundWidth: sized * 0.007,
                                  backgroundColor: swList[i]['state']
                                      ? Colors.transparent
                                      : AppColors.backgroundColor2,
                                  footer: Container(
                                    // height: 20,
                                    child: Text(
                                      SwPrivName.split('_')[int.parse(
                                          i.replaceFirst(RegExp(r'v'), ''))],
                                      // 'ssss',
                                      textDirection: TextDirection.rtl,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: swList[i]['state']
                                            ? AppColors.textColor2
                                            : AppColors.backgroundColor2,
                                        fontSize: swList[i]['state']
                                            ? sized * 0.011
                                            : sized * 0.011,
                                      ),
                                    ),
                                  ),
                                  center: IconButton(
                                    onPressed: () {
                                      switchTap(i, swList[i]['state']);
                                    },
                                    splashColor: Colors.transparent,
                                    icon: Icon(
                                      swList[i]['type'] == 'LIGHT'
                                          ? Icons.lightbulb_outline
                                          : swList[i]['type'] == 'VAN'
                                              ? Icons.storm_outlined
                                              : Icons.power_outlined,
                                      color: swList[i]['state']
                                          ? AppColors.textPrimary
                                              .withOpacity(0.8)
                                          : AppColors.textPrimary
                                              .withOpacity(0.4),
                                      size: sized * 0.03,
                                    ),
                                  ),
                                  linearGradient: const LinearGradient(
                                      begin: Alignment.topRight,
                                      end: Alignment.bottomLeft,
                                      colors: <Color>[
                                        Color(0xFF6DD400),
                                        Color(0xFF1AB600),
                                      ]),
                                  rotateLinearGradient: true,
                                  circularStrokeCap: CircularStrokeCap.round)),
                      ],
                    ),
                  ),
                ),
              ]
            : [],
        deviceState: deviceState,
        switchState: switchState);

Widget shortcutZain({
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
  var volume = 20,
  var hearing = 15,
  bool sil = true,
  bool lestin = true,
  bool play = true,
  required bool connect,
  required String ZName,
  bool Zmain = false,
  required Function() TapPlay,
  required Function() Play,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_hearingMute,
  required Function() doubleTap,
  required Function() tapOn_Switch_Icon,
  required Function(double?) vState,
  required Function(double?) hState,
}) =>
    GestureDetector(
        onDoubleTap: doubleTap,
        child: Container(
            margin: EdgeInsets.symmetric(
                horizontal: controller.sizedWidth * 0.055,
                vertical: controller.sizedHight * 0.0075),
            padding:
                EdgeInsets.symmetric(horizontal: controller.sizedWidth * 0.02),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(30)),
              color: AppColors.backgroundColor3,
            ),
            child: Directionality(
              textDirection: TextDirection.rtl,
              child: ExpansionTileCard(
                  duration: Duration(milliseconds: 400),
                  baseColor: Colors.transparent,
                  expandedColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  borderRadius: BorderRadius.all(Radius.circular(30)),
                  contentPadding: EdgeInsets.symmetric(
                      horizontal: controller.sizedWidth * 0.01),
                  trailing: connect != true
                      ? Padding(
                          padding: EdgeInsets.all(sized * 0.013),
                          child: txtStyle(
                              txt: 'غير متصل', color: AppColors.errorColor))
                      : Zmain
                          ? Padding(
                              padding:
                                  EdgeInsets.only(right: sizedWidth * 0.01),
                              child: IconButton(
                                onPressed: tapOn_Switch_Icon,
                                icon: iconStyle(
                                    icon: Icons.star_rounded,
                                    color: AppColors.warningColor),
                              ))
                          : Container(),
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      IconButton(
                        onPressed: tapOn_Switch_Icon,
                        icon: iconStyle(
                            icon: Icons.flutter_dash_outlined,
                            color: AppColors.warningColor),
                      ),
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.zero,
                          // padding: EdgeInsets.symmetric(horizontal: 5),
                          // color: Colors.blueGrey.shade600,
                          alignment: Alignment.bottomRight,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                padding:
                                    EdgeInsets.only(left: sizedWidth * 0.01),
                                decoration: BoxDecoration(
                                    border: Border(
                                        left: BorderSide(
                                            color: AppColors.textColor2
                                                .withOpacity(0.45),
                                            width: 1.5))),
                                child: txtStyle(
                                  txt: Zmain
                                      ? 'مساعد صوتي : رئيسي'
                                      : 'مساعد صوتي',
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.only(right: sized * 0.01),
                                child: txtStyle(
                                  txt: ZName,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  children: connect == true
                      ? [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              IconButton(
                                onPressed: Play,
                                padding: EdgeInsets.zero,
                                icon: iconStyle(
                                    icon: Icons.fast_forward_rounded,
                                    size: controller.sized * 0.04),
                              ),
                              SizedBox(
                                width: controller.sizedWidth * 0.07,
                              ),
                              IconButton(
                                padding: EdgeInsets.zero,
                                onPressed: TapPlay,
                                icon: iconStyle(
                                    icon: play
                                        ? Icons.play_arrow_rounded
                                        : Icons.pause_rounded,
                                    size: controller.sized * 0.04),
                              ),
                              SizedBox(
                                width: controller.sizedWidth * 0.07,
                              ),
                              IconButton(
                                onPressed: Play,
                                padding: EdgeInsets.zero,
                                icon: iconStyle(
                                    icon: Icons.fast_rewind_rounded,
                                    size: controller.sized * 0.04),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                child: Padding(
                                  padding:
                                      EdgeInsets.only(right: sizedWidth * 0.06),
                                  child: Slider(
                                      min: 0,
                                      max: 30,
                                      activeColor:
                                          AppColors.textColor.withOpacity(0.7),
                                      inactiveColor:
                                          AppColors.textColor.withOpacity(0.15),
                                      thumbColor:
                                          AppColors.textColor.withOpacity(0.95),
                                      value: volume.toDouble(),
                                      onChanged: vState),
                                ),
                              ),
                              Padding(
                                padding:
                                    EdgeInsets.only(left: sizedWidth * 0.025),
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: tapOn_VolumeMute,
                                  iconSize: sized * 0.032,
                                  icon: iconStyle(
                                    icon: sil
                                        ? Icons.volume_up_rounded
                                        : Icons.volume_off_rounded,
                                    color:
                                        AppColors.textColor.withOpacity(0.75),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding:
                                        EdgeInsets.only(right: sized * 0.02),
                                    child: Slider(
                                        min: 0,
                                        max: 30,
                                        activeColor: AppColors.textColor
                                            .withOpacity(0.7),
                                        inactiveColor: AppColors.textColor
                                            .withOpacity(0.15),
                                        thumbColor: AppColors.textColor
                                            .withOpacity(0.95),
                                        value: hearing.toDouble(),
                                        onChanged: hState),
                                  ),
                                ),
                                Padding(
                                  padding:
                                      EdgeInsets.only(left: sizedWidth * 0.025),
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    onPressed: tapOn_hearingMute,
                                    iconSize: sized * 0.032,
                                    icon: iconStyle(
                                      icon: lestin
                                          ? Icons.hearing_rounded
                                          : Icons.hearing_disabled_rounded,
                                      color:
                                          AppColors.textColor.withOpacity(0.75),
                                    ),
                                  ),
                                ),
                              ])
                        ]
                      : []),
            )));

Widget shortcutRoom(
        {required Function() onTap,
        required double sizedWidth,
        required double sizedHeight,
        required double sized,
        var roomState,
        required bool connect,
        required String roomName,
        String? roomPrivName,
        required Function(bool?) switchState,
        required var image,
        required}) =>
    GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
            horizontal: sizedWidth * 0.045, vertical: sizedHeight * 0.015),
        height: controller.sizedHight * 0.175,
        decoration: BoxDecoration(
          image: DecorationImage(
              image: image,
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(
                  AppColors.subtitleColor.withOpacity(0.25), BlendMode.darken)),

          borderRadius: const BorderRadius.all(Radius.circular(25)),
          // boxShadow: [
          //   BoxShadow(
          //     color: AppColors.textColor.withOpacity(0.7),
          //     spreadRadius: 2,
          //     blurRadius: 10,
          //     // offset: Offset(0, 0)
          //   )
          // ],
          // border: Border.all(width: 0.5, color: Colors.blueGrey.shade800)
        ),
        child: Column(
          children: [
            Row(
              children: [
                Padding(
                    padding: EdgeInsets.all(sized * 0.01),
                    child: connect == false
                        ? Padding(
                            padding: EdgeInsets.all(sized * 0.004),
                            child: Text(
                              'غير متصل',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                  color: AppColors.errorColor,
                                  fontSize: sized * 0.015,
                                  fontWeight: FontWeight.bold),
                            ))
                        : Directionality(
                            textDirection: TextDirection.rtl,
                            child: switchStyle(
                                value: roomState, onChanged: switchState),
                          )),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(
                        right: sizedWidth * 0.03, top: sizedHeight * 0.02),
                    height: controller.sizedHight * 0.15,
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          width: controller.sizedWidth * 0.6,
                          child: txtStyle(
                            align: TextAlign.right,
                            maxLines: 2,
                            txt: roomPrivName! == 'x'
                                ? 'لا يوجد اسم'
                                : roomPrivName.length > 12
                                    ? roomPrivName.substring(0, 14) + '...'
                                    : roomPrivName,
                            color: AppColors.textColor,
                            size: roomPrivName.length > 12
                                ? sized * 0.023
                                : sized * 0.025,
                          ),
                        ),
                        SizedBox(
                          height: controller.sizedHight * 0.01,
                        ),
                        txtStyle(
                          txt: roomName,
                          color: AppColors.textColor.withOpacity(0.6),
                          size: sized * 0.013,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
