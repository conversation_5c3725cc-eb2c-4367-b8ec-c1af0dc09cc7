import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';

HomeController controller = Get.find();
Map roomdevices = {};
var roomId;
var room;
var homeType;
var roomImage;
var roomState;

void switchTap(String Switch, bool state, String id) {
  // setState(() {
  print(11111111111);
  // print(controller.rooms[roomId]['devices'][id][Switch]);
  controller.rooms[roomId]['devices'][id][Switch] = !state;
  // });
}
