// اختبار أوامر المكيف - للتحقق من صحة الأوامر المرسلة

void testACCommands() {
  print('=== اختبار أوامر المكيف ===\n');

  // بيانات وهمية للمكيف
  Map<String, dynamic> acDevice = {
    'id': 'AC_001',
    'degree': 24,
    'speed': 2,
    'type': 'تبريد',
    'state': true,
  };

  print('🔧 اختبار الأوامر المختلفة:\n');

  // اختبار وضع التبريد
  print('1️⃣ وضع التبريد:');
  acDevice['type'] = 'تبريد';
  String coolCommand = generateACCommand(acDevice, true);
  print('   الأمر المرسل: $coolCommand');
  print('   المتوقع: AC_001 AC RUN 24 VAN 2 AC\n');

  // اختبار وضع التدفئة
  print('2️⃣ وضع التدفئة:');
  acDevice['type'] = 'تدفئه';
  String heatCommand = generateACCommand(acDevice, true);
  print('   الأمر المرسل: $heatCommand');
  print('   المتوقع: AC_001 AC RUN 24 VAN 2 HEAT\n');

  // اختبار وضع المروحة (المشكلة المصلحة)
  print('3️⃣ وضع المروحة (المصلح):');
  acDevice['type'] = 'مروحه';
  String fanCommand = generateACCommand(acDevice, true);
  print('   الأمر المرسل: $fanCommand');
  print('   المتوقع: AC_001 AC RUN X VAN 2 VAN');
  print('   ملاحظة: درجة الحرارة = X لأن المروحة لا تحتاج درجة حرارة\n');

  // اختبار إيقاف التشغيل
  print('4️⃣ إيقاف التشغيل:');
  String offCommand = generateACCommand(acDevice, false);
  print('   الأمر المرسل: $offCommand');
  print('   المتوقع: AC_001 AC OFF\n');

  // اختبار سرعات مختلفة
  print('5️⃣ اختبار سرعات المروحة:');
  acDevice['type'] = 'تبريد';
  for (int speed = 1; speed <= 4; speed++) {
    acDevice['speed'] = speed;
    String speedCommand = generateACCommand(acDevice, true);
    print('   السرعة $speed: $speedCommand');
  }
  print('');

  // اختبار درجات حرارة مختلفة
  print('6️⃣ اختبار درجات الحرارة:');
  acDevice['speed'] = 2;
  List<int> temperatures = [16, 20, 24, 28, 30];
  for (int temp in temperatures) {
    acDevice['degree'] = temp;
    String tempCommand = generateACCommand(acDevice, true);
    print('   ${temp}°C: $tempCommand');
  }
}

// دالة لتوليد الأمر (نسخة من المنطق المصلح)
String generateACCommand(Map<String, dynamic> ac, bool command) {
  if (command == true) {
    // تحديد درجة الحرارة حسب نوع التشغيل
    String temperature = ac['type'].toString() == 'مروحه'
        ? 'X'
        : (ac['degree'] != null ? ac['degree'].toInt().toString() : 'X');
    
    // تحديد سرعة المروحة
    String fanSpeed = ac['speed'] != null ? ac['speed'].toString() : '0';
    
    // تحديد نوع التشغيل
    String acType;
    switch (ac['type'].toString()) {
      case 'تبريد':
        acType = 'AC';
        break;
      case 'تدفئه':
        acType = 'HEAT';
        break;
      case 'مروحه':
        acType = 'VAN';
        break;
      default:
        acType = 'XX';
    }
    
    return '${ac['id']} AC RUN $temperature VAN $fanSpeed $acType';
  } else {
    return '${ac['id']} AC OFF';
  }
}

void main() {
  testACCommands();
}
