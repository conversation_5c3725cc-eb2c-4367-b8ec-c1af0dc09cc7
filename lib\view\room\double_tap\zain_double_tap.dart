import 'package:flutter/cupertino.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/modules/local/alarm.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/models/pages.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/view/room/double_tap/edit_room.dart';

import 'del.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Future<dynamic> zainDoubleTap({
  required var context,
  required var i,
}) async {
  final conn = await MySqlConnection.connect(ConnectionSettings(
      host: controller.hostZain.value,
      // port: 80,
      user: 'root',
      db: 'zain',
      password: 'zain',
      characterSet: CharacterSet.UTF8));
  var appDB = await openDatabase('${controller.system}.db', version: 3);

  Map rooms = {};

  var selectroom = 0;
  var selectname = 0;

  for (var r = 0; r < controller.rooms.keys.toList().length; r++) {
    rooms[controller.rooms[controller.rooms.keys.toList()[r]]['id']] =
        controller.rooms[controller.rooms.keys.toList()[r]]['privName'];
    if (roomId == controller.rooms[controller.rooms.keys.toList()[r]]['id']) {
      selectroom = r;
    }
  }
  isScheduler = false;

  isAM = false;
  h = DateTime.now().hour;
  if (h > 12) {
    h = h - 12;
    isAM = false;
  } else {
    isAM = true;
  }
  m = DateTime.now().minute;
  days = [];
  re = false;
  allday = false;

  showBottomSheet(
      enableDrag: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return GetBuilder<HomeController>(
            builder: (controller) =>
                StatefulBuilder(builder: ((context, setState) {
                  return ZainPage(
                      id: i['id'],
                      editRoom: () {
                        x() async {
                          var s = await editRoom(
                              context: context,
                              selectroom: selectroom,
                              rooms: rooms,
                              i: i);
                          s = await s.toString();
                          if (selectroom != int.parse(s)) {
                            selectroom = await int.parse(s);
                            final builder = MqttClientPayloadBuilder();
                            builder.addString('1');
                            await client.publishMessage(
                                'edit', MqttQos.atLeastOnce, builder.payload!);
                            await builder.clear();
                            await builder.addString('re');
                            await client.publishMessage(
                                i['id'], MqttQos.atLeastOnce, builder.payload!);
                            Navigator.of(context).pop();
                          }
                        }

                        x();
                      },
                      addAlarm: () async {
                        await alarm(
                            txt: 'المنبه',
                            context: context,
                            setState1: setState,
                            submit: () async {
                              String wdays = '';
                              if (days.isEmpty) {
                                wdays = 'None';
                              } else {
                                for (var i in days) {
                                  wdays += weekDays[i] + ' ';
                                }
                                wdays = wdays.substring(0, wdays.length - 1);
                              }
                              print(wdays);
                              String clock = h.toString() +
                                  ':' +
                                  (m > 9 ? m.toString() : '0' + m.toString());
                              String time = isAM ? 'AM' : 'PM';
                              String ree = re ? 'ON' : 'OFF';

                              await conn.query(
                                  "INSERT INTO Alarm(wday,clock,re,state,room) values(?,?,?,?,?)",
                                  [
                                    wdays,
                                    '$clock $time',
                                    ree,
                                    'ON',
                                    rooms.keys.toList()[selectroom]
                                  ]);
                              print(
                                  '2222222222222222222222222222222222222222222222222222222222222222');
                              final builder = MqttClientPayloadBuilder();
                              builder.addString('1');
                              client.publishMessage('edit', MqttQos.atLeastOnce,
                                  builder.payload!);
                              setState(
                                () {
                                  isScheduler = false;
                                  isAM = false;
                                  h = DateTime.now().hour;
                                  if (h > 12) {
                                    h = h - 12;
                                    isAM = false;
                                  } else {
                                    isAM = true;
                                  }
                                  m = DateTime.now().minute;
                                  days = [];
                                  re = false;
                                  allday = false;
                                },
                              );
                              print(
                                  '3333333333333333333333333333333333333333333333333');
                            });
                      },
                      editAlarm:
                          (alarmId, hh, mm, isAMM, ddays, alldayy, ree) async {
                        print(ddays);
                        isScheduler = true;
                        h = hh!;
                        m = mm!;
                        isAM = isAMM!;
                        days = ddays!;
                        allday = alldayy!;
                        re = ree!;
                        await alarm(
                            txt: 'منبة',
                            context: context,
                            setState1: setState,
                            submit: () async {
                              String wdays = '';
                              print(days);
                              print(
                                  '1111111111111111111111111111111111111111111111111111');
                              if (days.isEmpty) {
                                wdays = 'None';
                              } else {
                                for (var i in days) {
                                  wdays += weekDays[i] + ' ';
                                }
                                wdays = wdays.substring(0, wdays.length - 1);
                              }
                              print(wdays);
                              String clock = h.toString() +
                                  ':' +
                                  (m > 9 ? m.toString() : '0' + m.toString());
                              String time = isAM ? 'AM' : 'PM';
                              String ree = re ? 'ON' : 'OFF';

                              await conn.query(
                                  "UPDATE Alarm SET wday=? , clock=? , re = ? , state = ? , room = ? WHERE id = ?",
                                  [
                                    wdays,
                                    '$clock $time',
                                    ree,
                                    'ON',
                                    rooms.keys.toList()[selectroom],
                                    alarmId
                                  ]);
                              final builder = MqttClientPayloadBuilder();
                              builder.addString('1');
                              client.publishMessage('edit', MqttQos.atLeastOnce,
                                  builder.payload!);
                            },
                            del: () async {
                              await conn.query(
                                  "DELETE FROM Alarm  WHERE id = ?", [alarmId]);
                              final builder = MqttClientPayloadBuilder();
                              builder.addString('1');
                              client.publishMessage('edit', MqttQos.atLeastOnce,
                                  builder.payload!);
                            });
                        setState(
                          () {
                            isScheduler = false;
                            isAM = false;
                            h = DateTime.now().hour;
                            if (h > 12) {
                              h = h - 12;
                              isAM = false;
                            } else {
                              isAM = true;
                            }
                            m = DateTime.now().minute;
                            days = [];
                            re = false;
                            allday = false;
                          },
                        );
                      },
                      Dfavorite: () {
                        Dfavorite(
                            context: context,
                            device: i['id'],
                            appDB: appDB,
                            state: true);
                      },
                      alarmList:
                          controller.alarms[rooms.keys.toList()[selectroom]],
                      connect: true,
                      ZName: i['pubName'],
                      Zmain: i['device'] == 'ZAIN-Main' ? true : false,
                      roomN: rooms[rooms.keys.toList()[selectroom]],
                      sizedWidth: controller.sizedWidth,
                      sizedHeight: controller.sizedHight,
                      sized: controller.sized,
                      conn: conn);
                })));
      });
}
