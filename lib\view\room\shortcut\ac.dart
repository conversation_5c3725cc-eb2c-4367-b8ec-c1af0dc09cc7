import 'package:zaen/shared/commands/ac.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/models/shortcuts.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/view/room/double_tap/ac_double_tap.dart';
import 'package:zaen/shared/themes/app_colors.dart';

roomAc({
  required var context,
  required var i,
}) =>
    shortcutAc(
        // image: AssetImage("assets/images/ac.jpg"),
        sizedWidth: controller.sizedWidth,
        sizedHeight: controller.sizedHight,
        sized: controller.sized,
        connect: controller.devices[i['id']],
        deviceState: i['state'],
        degree: i['degree'],
        typeState: i['type'] == 'تدفئه'
            ? 0
            : i['type'] == 'تبريد'
                ? 1
                : 2,
        speedState: i['speed'] - 1,
        swingState: i['swing'],
        acPrivName: i['priv'],
        doubleTap: () {
          acDoubleTap(context: context, i: i);
        },
        tapOn_Ac_Icon: () {
          print('يعرض حاله المكيف');
        },
        acRun: () {
          if (client.connectionStatus!.state.name == 'connected') {
            print('RUN ' +
                i['degree'].toInt().toString() +
                ' ' +
                i['type'].toString() +
                ' van ' +
                i['speed'].toString());
            if (controller.rooms[roomId]['devices'][i['id']]['state'] == false)
              switchTap('state', i['state'], i['id']);
            roomState = false;
            for (var j in controller.rooms[roomId]['devices'].values) {
              if (j['state'] == true) {
                roomState = true;
              }
              // setState(() {

              // });
            }
            controller.rooms[roomId]['state'] = roomState;
            if (roomState == true) {
              controller.homeState = true;
            } else {
              controller.homeState = false;
              for (var i in controller.rooms.values) {
                if (i['state'] == true) {
                  controller.homeState = true;
                }
              }
            }
            final builder = MqttClientPayloadBuilder();
            builder.addString(i['id'] +
                ' AC RUN ' +
                (i['type'].toString() == 'مروحه'
                    ? 'X'
                    : i['degree'].toInt().toString()) +
                ' VAN ' +
                i['speed'].toString() +
                ' ' +
                (i['type'].toString() == 'تبريد'
                    ? 'AC'
                    : i['type'].toString() == 'تدفئه'
                        ? 'HEAT'
                        : 'VAN'));
            client.publishMessage(controller.homeId + "/app/zain",
                MqttQos.atLeastOnce, builder.payload!);
            controller.update();
          }
        },
        switchState: (val) {
          commandAc(val!, i, roomId);
          // if (client.connectionStatus!.state.name == 'connected') {
          //   print('يرسل الامر الى السيرفر ');
          //   // getRoomDevices();
          //   switchTap('state', i['state'], i['id']);
          //   roomState = false;
          //   for (var j in controller.rooms[roomId]['devices'].values) {
          //     if (j['state'] == true) {
          //       roomState = true;
          //     }
          //     // setState(() {

          //     // });
          //   }
          //   controller.rooms[roomId]['state'] = roomState;
          //   if (val == true) {
          //     controller.homeState = true;
          //   } else {
          //     controller.homeState = false;
          //     for (var i in controller.rooms.values) {
          //       if (i['state'] == true) {
          //         controller.homeState = true;
          //       }
          //     }
          //   }
          //   final builder = MqttClientPayloadBuilder();

          //   if (val == true) {
          //     builder.addString(i['id'] +
          //         ' AC RUN ' +
          //         i['degree'].toInt().toString() +
          //         ' VAN ' +
          //         i['speed'].toString() +
          //         ' ' +
          //         (i['type'].toString() == 'تبريد'
          //             ? 'AC'
          //             : i['type'].toString() == 'تدفئه'
          //                 ? 'HEAT'
          //                 : 'VAN'));
          //   } else {
          //     builder.addString(i['id'] + ' AC OFF');
          //   }
          //   client.publishMessage(controller.homeId + "/app/zain",
          //       MqttQos.atLeastOnce, builder.payload!);

          //   controller.update();
          // }
        },
        sliderState: (val) {
          // setState(() {
          if (client.connectionStatus!.state.name == 'connected') {
            controller.rooms[roomId]['devices'][i['id']]['degree'] = val;
            // });
            controller.update();
          }
        },
        acTypeState: (val) {
          if (client.connectionStatus!.state.name == 'connected') {
            print(val);
            // setState(() {
            controller.rooms[roomId]['devices'][i['id']]['type'] = val == 0
                ? 'تدفئه'
                : val == 1
                    ? 'تبريد'
                    : 'مروحه';
            // });
            controller.update();
          }
        },
        acSwingState: () {
          if (client.connectionStatus!.state.name == 'connected') {
            switchTap('swing', i['swing'], i['id']);
            controller.update();
          }
        },
        acSpeedsStateLeft: () {
          // setState(() {
          if (client.connectionStatus!.state.name == 'connected') {
            controller.rooms[roomId]['devices'][i['id']]['speed'] =
                i['speed'] != 1 ? i['speed'] - 1 : i['speed'];
            // });
            controller.update();
          }
        },
        acSpeedsStateRight: () {
          // setState(() {
          if (client.connectionStatus!.state.name == 'connected') {
            controller.rooms[roomId]['devices'][i['id']]['speed'] =
                i['speed'] != 4 ? i['speed'] + 1 : i['speed'];
            // });
            controller.update();
          }
        });
