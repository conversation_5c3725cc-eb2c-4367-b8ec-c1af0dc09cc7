import 'package:flutter/material.dart';
import 'package:zaen/shared/commands/tv.dart';
import 'package:zaen/shared/components/components.dart';
import '../../../../shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget TV({
  device,
  room,
  required Function() tapOn_VolumeUp,
  required Function() tapOn_VolumeDown,
  required Function() tapOn_ChUp,
  required Function() tapOn_ChDown,
  required Function() tapOn_VolumeMute,
  required Function() tapOn_123,
  required Function() tapOn_menu,
  required Function() tapOn_star,
  required bool sil,
}) {
  print(device);
  roomId = room;

  return Directionality(
    textDirection: TextDirection.rtl,
    child: Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.all(controller.sized * 0.01),
                child: iconStyle(
                    icon: Icons.tv_rounded, color: AppColors.warningColor),
              ),
              Container(
                margin: EdgeInsets.zero,
                // padding:
                //     EdgeInsets.only(right: controller.sizedWidth * 0.01),
                // color: Colors.blueGrey.shade600,
                // alignment: Alignment.bottomRight,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          padding: EdgeInsets.only(
                            left: controller.sizedWidth * 0.008,
                          ),
                          decoration: BoxDecoration(
                              border: Border(
                                  left: BorderSide(
                                      color: AppColors.textColor3
                                          .withOpacity(0.45),
                                      width: 1))),
                          child: txtStyle(
                            align: TextAlign.right,
                            txt: 'تلفاز',
                          ),
                        ),
                        Container(
                            width: controller.sizedWidth * 0.35,
                            padding: EdgeInsets.only(
                                right: controller.sizedWidth * 0.01),
                            child: txtStyle(
                              align: TextAlign.right,
                              txt: device['priv'] == 'x'
                                  ? 'لا يوجد اسم'
                                  : device['priv'].split('_')[0].length > 14
                                      ? device['priv']
                                              .split('_')[0]
                                              .substring(0, 13) +
                                          '...'
                                      : device['priv'].split('_')[0],
                            )),
                      ],
                    ),
                    SizedBox(
                      height: controller.sizedHight * 0.005,
                    ),
                    Row(
                      textDirection: TextDirection.rtl,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        txtStyle(txt: controller.rooms[room]['privName']),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.all(controller.sized * 0.008),
                child: switchStyle(
                    value: device['state'] ?? false,
                    onChanged: (val) {
                      roomId = room;
                      commandTvSw(
                          val!,
                          controller.rooms[room]['devices'][device['id']],
                          room);
                    }),
              )
            ],
          ),
        ),
        Column(
          children: [
            SizedBox(
              height: controller.sizedHight * 0.04,
            ),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Container(
                    width: controller.sizedWidth * 0.13,
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor.withOpacity(0.25),
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                    ),
                    child: Column(
                      children: [
                        GestureDetector(
                          onTap: tapOn_ChUp,
                          child: Icon(Icons.arrow_drop_up,
                              size: controller.sized * 0.03,
                              color: AppColors.textColor3),
                        ),
                        SizedBox(
                          height: controller.sizedHight * 0.015,
                        ),
                        IconButton(
                          padding: EdgeInsets.zero,
                          onPressed: tapOn_menu,
                          iconSize: controller.sized * 0.035,
                          icon: Icon(
                            Icons.swap_horiz_rounded,
                            color: AppColors.textColor2,
                          ),
                        ),
                        SizedBox(
                          height: controller.sizedHight * 0.015,
                        ),
                        GestureDetector(
                          onTap: tapOn_ChDown,
                          child: Icon(Icons.arrow_drop_down,
                              size: controller.sized * 0.03,
                              color: AppColors.textColor3),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: controller.sizedWidth * 0.1,
                  ),
                  Container(
                    // padding: EdgeInsets.o,
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor.withOpacity(0.25),
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                    ),
                    child: Column(
                      children: [
                        SizedBox(
                          height: controller.sizedHight * 0.006,
                        ),
                        IconButton(
                          padding: EdgeInsets.zero,
                          onPressed: tapOn_star,
                          icon: const Icon(
                            Icons.important_devices_rounded,
                          ),
                          iconSize: controller.sized * 0.035,
                          color: AppColors.textColor2,
                        ),
                        SizedBox(
                          width: controller.sizedWidth * 0.15,
                          height: controller.sized * 0.027,
                        ),
                        IconButton(
                          padding: EdgeInsets.only(bottom: 5),
                          onPressed: tapOn_123,
                          icon: const Icon(Icons.pin_rounded),
                          iconSize: controller.sized * 0.035,
                          color: AppColors.textColor2,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: controller.sizedWidth * 0.1,
                  ),
                  Container(
                    width: controller.sizedWidth * 0.13,
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor.withOpacity(0.25),
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                    ),
                    child: Column(
                      children: [
                        GestureDetector(
                          onTap: tapOn_VolumeUp,
                          child: Icon(Icons.arrow_drop_up,
                              size: controller.sized * 0.03,
                              color: AppColors.textColor3),
                        ),
                        SizedBox(
                          height: controller.sizedHight * 0.015,
                        ),
                        IconButton(
                          padding: EdgeInsets.zero,
                          onPressed: tapOn_VolumeMute,
                          iconSize: controller.sized * 0.032,
                          icon: Icon(
                            sil
                                ? Icons.volume_up_rounded
                                : Icons.volume_off_rounded,
                            color: AppColors.textColor2,
                          ),
                        ),
                        SizedBox(
                          height: controller.sizedHight * 0.015,
                        ),
                        GestureDetector(
                          onTap: tapOn_VolumeDown,
                          child: Icon(Icons.arrow_drop_down,
                              size: controller.sized * 0.03,
                              color: AppColors.textColor3),
                        ),
                      ],
                    ),
                  ),
                  // SizedBox(
                  //   width: 20,
                  // ),
                ],
              ),
            ),
            SizedBox(
              height: 10,
            ),
          ],
        )
      ],
    ),
  );
}
