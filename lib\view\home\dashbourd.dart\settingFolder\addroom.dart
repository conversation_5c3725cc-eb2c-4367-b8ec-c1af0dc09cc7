// ignore_for_file: unnecessary_null_comparison

import 'dart:convert';
import 'dart:io';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/view/home/<USER>/routine.dart';

addRoom(context) async {
  if (client.connectionStatus!.state.name == 'connected') {
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));
    conn.close();
    List names = [];
    String string = 'add';
    List getNames = [];

    for (var n in getNames) {
      if (n != null) {
        names.add(n);
      }
    }
    if (names.length == 0) {
      string = 'add';
    }
    GlobalKey<FormState> kname1 = new GlobalKey<FormState>();
    TextEditingController name1 = TextEditingController();

    GlobalKey<FormState> kname2 = new GlobalKey<FormState>();
    TextEditingController name2 = TextEditingController();

    TextEditingController name = TextEditingController(
      text: string == 'edit' ? names[0] : '',
    );
    bool edit = false;

    print(names);
    int selectname = 0;
    PageController pageController = PageController();

    String image = 'assets/images/places/home/<USER>';

    AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      headerAnimationLoop: true,
      animType: AnimType.topSlide,
      dialogBackgroundColor: AppColors.backgroundColor2,

      body: Center(
        child: Material(
          color: Colors.transparent,
          child: SizedBox(
            height: controller.sizedHight * 0.42,
            child: PageView(
                physics: NeverScrollableScrollPhysics(),
                controller: pageController,
                children: [
                  Column(
                    children: [
                      Text(
                        'ادخل اسم عام للغرفة',
                        style: TextStyle(
                            color: AppColors.textColor.withOpacity(0.7),
                            fontSize: controller.sized * 0.015,
                            fontWeight: FontWeight.bold),
                      ),
                      Form(
                        key: kname1,
                        child: Container(
                          width: controller.sizedWidth * 0.85,
                          child: TextFormField(
                            controller: name1,
                            validator: (val) {
                              if (name1.text == '' || name1.text == null) {
                                return 'قم بادخال اسم عام للغرفة';
                              } else {
                                for (var i = 0; i < name1.text.length; i++) {
                                  if (arabic.contains(name1.text[i]) ||
                                      name1.text[i].isNumericOnly) {
                                    edit = true;
                                  } else {
                                    return 'قم بادخال حروف عربية او ارقام فقط';
                                  }
                                }
                              }
                              return null;
                            },
                            maxLength: 25,
                            autofocus: true,
                            showCursor: true,
                            cursorColor: AppColors.primary,
                            textDirection: TextDirection.rtl,
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: controller.sized * 0.015,
                              fontWeight: FontWeight.w500,
                            ),
                            onEditingComplete: () {
                              FocusManager.instance.primaryFocus?.unfocus();
                              var formdata = kname1.currentState;
                              formdata!.validate();
                            },
                            decoration: InputDecoration(
                              hintText: 'اسم عام للغرفة',
                              hintStyle: TextStyle(
                                color: AppColors.textHint,
                                fontSize: controller.sized * 0.014,
                                fontWeight: FontWeight.normal,
                              ),
                              filled: true,
                              fillColor: AppColors.surface,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.04,
                                vertical: controller.sizedHight * 0.015,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.border,
                                  width: 1.0,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.border,
                                  width: 1.0,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.primary,
                                  width: 2.0,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.error,
                                  width: 1.5,
                                ),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.error,
                                  width: 2.0,
                                ),
                              ),
                              suffixIcon: Icon(
                                Icons.edit_rounded,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: controller.sizedHight * 0.075,
                      ),
                      submitButtom(
                          text: 'التالي',
                          onPressed: () {
                            var formdata = kname1.currentState;
                            if (formdata!.validate()) {
                              FocusManager.instance.primaryFocus?.unfocus();
                              pageController.nextPage(
                                  duration: Duration(milliseconds: 650),
                                  curve: Curves.ease);
                            } else {
                              // pageController.nextPage(duration: Duration(milliseconds: 100), curve: Curves.ease);
                            }
                          })
                    ],
                  ),
                  Column(
                    children: [
                      Row(
                        children: [
                          MaterialButton(
                              minWidth: controller.sizedWidth * 0.001,
                              padding: EdgeInsets.only(
                                  right: controller.sizedWidth * 0.09),
                              onPressed: () {
                                FocusManager.instance.primaryFocus?.unfocus();
                                pageController.previousPage(
                                    duration: Duration(milliseconds: 650),
                                    curve: Curves.ease);
                              },
                              child: Icon(
                                Icons.arrow_back_ios_rounded,
                                size: controller.sized * 0.02,
                                color: Colors.blue,
                              )),
                          Expanded(
                            child: Text(
                              'ادخل اسم خاص للغرفة',
                              textAlign: TextAlign.center,
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                  color: AppColors.textColor.withOpacity(0.7),
                                  fontSize: controller.sized * 0.015,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                          Container(
                            width: controller.sizedWidth * 0.1,
                          )
                        ],
                      ),
                      Form(
                        key: kname2,
                        child: Container(
                          width: controller.sizedWidth * 0.85,
                          child: TextFormField(
                            controller: name2,
                            validator: (val) {
                              if (name2.text == '' || name2.text == null) {
                                return 'قم بادخال اسم خاص للغرفة';
                              } else {
                                for (var i = 0; i < name2.text.length; i++) {
                                  if (arabic.contains(name2.text[i]) ||
                                      name2.text[i].isNumericOnly) {
                                    edit = true;
                                  } else {
                                    return 'قم بادخال حروف عربية او ارقام فقط';
                                  }
                                }
                              }
                              return null;
                            },
                            maxLength: 25,
                            autofocus: true,
                            showCursor: true,
                            cursorColor: AppColors.primary,
                            textDirection: TextDirection.rtl,
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: controller.sized * 0.015,
                              fontWeight: FontWeight.w500,
                            ),
                            onEditingComplete: () {
                              FocusManager.instance.primaryFocus?.unfocus();
                              var formdata = kname2.currentState;
                              formdata!.validate();
                            },
                            decoration: InputDecoration(
                              hintText: 'اسم خاص للغرفة',
                              hintStyle: TextStyle(
                                color: AppColors.textHint,
                                fontSize: controller.sized * 0.014,
                                fontWeight: FontWeight.normal,
                              ),
                              filled: true,
                              fillColor: AppColors.surface,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.04,
                                vertical: controller.sizedHight * 0.015,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.border,
                                  width: 1.0,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.border,
                                  width: 1.0,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.primary,
                                  width: 2.0,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.error,
                                  width: 1.5,
                                ),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.error,
                                  width: 2.0,
                                ),
                              ),
                              suffixIcon: Icon(
                                Icons.edit_rounded,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: controller.sizedHight * 0.06,
                      ),
                      submitButtom(
                          text: 'التالي',
                          onPressed: () {
                            var formdata = kname2.currentState;
                            if (formdata!.validate()) {
                              FocusManager.instance.primaryFocus?.unfocus();
                              pageController.nextPage(
                                  duration: Duration(milliseconds: 650),
                                  curve: Curves.ease);
                            } else {
                              // pageController.nextPage(duration: Duration(milliseconds: 100), curve: Curves.ease);
                            }
                          })
                    ],
                  ),
                  StatefulBuilder(
                    builder: ((context, setState) {
                      return Column(
                        children: [
                          Row(
                            children: [
                              MaterialButton(
                                  minWidth: controller.sizedWidth * 0.001,
                                  padding: EdgeInsets.only(
                                      right: controller.sizedWidth * 0.09),
                                  onPressed: () {
                                    FocusManager.instance.primaryFocus
                                        ?.unfocus();
                                    pageController.previousPage(
                                        duration: Duration(milliseconds: 650),
                                        curve: Curves.ease);
                                  },
                                  child: Icon(
                                    Icons.arrow_back_ios_rounded,
                                    size: controller.sized * 0.02,
                                    color: Colors.blue,
                                  )),
                              Expanded(
                                child: Text(
                                  'اختر صورة للغرفة',
                                  textAlign: TextAlign.center,
                                  textDirection: TextDirection.rtl,
                                  style: TextStyle(
                                      color:
                                          AppColors.textColor.withOpacity(0.7),
                                      fontSize: controller.sized * 0.015,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              Container(
                                width: controller.sizedWidth * 0.1,
                              )
                            ],
                          ),

                          Container(
                            height: controller.sizedHight * 0.093,
                            width: controller.sizedWidth * 0.4,
                            child: image.contains('com.example.zaen')
                                ? Image.file(
                                    File(image),
                                    color: AppColors.subtitleColor
                                        .withOpacity(0.2),
                                    colorBlendMode: BlendMode.darken,
                                    fit: BoxFit.cover,
                                    filterQuality: FilterQuality.high,
                                  )
                                : Image.asset(
                                    "$image",
                                    color: AppColors.subtitleColor
                                        .withOpacity(0.2),
                                    colorBlendMode: BlendMode.darken,
                                    fit: BoxFit.cover,
                                    filterQuality: FilterQuality.high,
                                  ),
                          ),
                          // SizedBox(height: controller.sizedHight * 0.006),
                          MaterialButton(
                            padding: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.01),
                            onPressed: () async {
                              final manifestContent = await rootBundle
                                  .loadString('AssetManifest.json');
                              Map<String, dynamic> manifestMap =
                                  await json.decode(manifestContent);
                              // >> To get paths you need these 2 lines
                              List imagePaths = await manifestMap.keys
                                  .where((String key) => key.contains(
                                      'images/places/${controller.homeType.value}/'))
                                  .toList();
                              print(imagePaths);
                              var item = imagePaths.indexOf(image);
                              if (item == -1) {
                                item = imagePaths.length;
                              }
                              print(item);

                              await showCupertinoModalPopup(
                                  context: context,
                                  barrierColor:
                                      AppColors.textColor2.withOpacity(0.35),
                                  builder: (builder) {
                                    return Center(
                                      child: Container(
                                          width: controller.sizedWidth,
                                          height: controller.sizedHight * 0.3,
                                          decoration: BoxDecoration(
                                            color: AppColors.backgroundColor2
                                                .withOpacity(0.975),
                                          ),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              StatefulBuilder(
                                                  builder:
                                                      ((context, setState) =>
                                                          Expanded(
                                                            child: RotatedBox(
                                                              quarterTurns: 1,
                                                              child: Center(
                                                                child: ListWheelScrollView(
                                                                    magnification: 5,
                                                                    // useMagnifier: true,
                                                                    controller: FixedExtentScrollController(initialItem: item),
                                                                    // magnification: 2.0,
                                                                    itemExtent: controller.sizedWidth * 0.55,
                                                                    diameterRatio: 20,
                                                                    squeeze: 0.88,
                                                                    // overAndUnderCenterOpacity: 0.5,
                                                                    // scrollDirection:
                                                                    //     Axis.horizontal,
                                                                    // reverse: true,
                                                                    onSelectedItemChanged: (x) {
                                                                      print(x);
                                                                      setState(
                                                                          () {
                                                                        item =
                                                                            x;
                                                                      });
                                                                    },
                                                                    children: List.generate(
                                                                      imagePaths
                                                                          .length,
                                                                      (index) =>
                                                                          RotatedBox(
                                                                        quarterTurns:
                                                                            -1,
                                                                        child:
                                                                            Center(
                                                                          child:
                                                                              AnimatedContainer(
                                                                            duration:
                                                                                Duration(milliseconds: 450),
                                                                            width: item == index
                                                                                ? controller.sizedWidth * 0.7
                                                                                : controller.sizedWidth * 0.5,
                                                                            height: item == index
                                                                                ? controller.sizedHight * 0.16
                                                                                : controller.sizedHight * 0.13,
                                                                            decoration: BoxDecoration(
                                                                                image: DecorationImage(
                                                                              image: AssetImage(imagePaths[index].toString()),
                                                                              onError: (context, stackTrace) {},
                                                                              colorFilter: ColorFilter.mode(
                                                                                item == index ? AppColors.textColor.withOpacity(0.1) : AppColors.subtitleColor.withOpacity(0.6),
                                                                                BlendMode.darken,
                                                                              ),
                                                                              fit: BoxFit.cover,
                                                                              filterQuality: FilterQuality.high,
                                                                            )),
                                                                            //   child:Image.asset(
                                                                            //   imagePaths[index].toString(),
                                                                            //   errorBuilder: (context, error, stackTrace) {
                                                                            //     return Container();
                                                                            //   },
                                                                            //   colorBlendMode: BlendMode.darken,
                                                                            //   fit: BoxFit.cover,
                                                                            //   filterQuality: FilterQuality.high,
                                                                            // )
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    )),
                                                              ),
                                                            ),
                                                          ))),
                                              submitButtom(
                                                onPressed: () async {
                                                  setState(
                                                    () {
                                                      if (Navigator.of(context)
                                                          .canPop()) {
                                                        Navigator.of(context)
                                                            .pop();
                                                      }
                                                      image = imagePaths[item];
                                                    },
                                                  );
                                                },
                                              ),
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.01,
                                              )
                                            ],
                                          )),
                                    );
                                  });
                            },
                            child:
                                Row(mainAxisSize: MainAxisSize.min, children: [
                              Icon(
                                Icons.arrow_back_ios,
                                size: controller.sized * 0.015,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              Text(
                                'اختيار صوره من الموجود',
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                    fontSize: controller.sized * 0.0125,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        AppColors.textColor.withOpacity(0.6)),
                              ),
                            ]),
                          ),
                          // SizedBox(height: controller.sizedHight * 0.001),
                          MaterialButton(
                            padding: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.01),
                            onPressed: () async {
                              XFile? ximage = await ImagePicker()
                                  .pickImage(source: ImageSource.gallery);
                              if (ximage == null) {
                                print('555555555555555555555555');
                                return;
                              }
                              final imageTemp = File(ximage.path);
                              print(imageTemp.path);
                              setState(() {
                                image = imageTemp.path;
                              });
                            },
                            child:
                                Row(mainAxisSize: MainAxisSize.min, children: [
                              Icon(
                                Icons.camera_rounded,
                                size: controller.sized * 0.015,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              Text(
                                'اختيار صوره من البوم الصور',
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                    fontSize: controller.sized * 0.0125,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        AppColors.textColor.withOpacity(0.6)),
                              ),
                            ]),
                          ),
                          // SizedBox(height: controller.sizedHight * 0.001),
                          MaterialButton(
                            padding: EdgeInsets.symmetric(
                                horizontal: controller.sizedWidth * 0.01),
                            onPressed: () async {
                              XFile? ximage = await ImagePicker()
                                  .pickImage(source: ImageSource.camera);
                              if (ximage == null) {
                                print('555555555555555555555555');
                                return;
                              }
                              final imageTemp = File(ximage.path);
                              print(imageTemp.path);
                              setState(() {
                                image = imageTemp.path;
                              });
                            },
                            child:
                                Row(mainAxisSize: MainAxisSize.min, children: [
                              Icon(
                                Icons.camera_alt_rounded,
                                size: controller.sized * 0.015,
                                color: AppColors.textColor.withOpacity(0.6),
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              Text(
                                'التقاط صورة',
                                textDirection: TextDirection.rtl,
                                style: TextStyle(
                                    fontSize: controller.sized * 0.0125,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        AppColors.textColor.withOpacity(0.6)),
                              ),
                            ]),
                          ),
                          submitButtom(
                              text: 'اضافة الغرفة',
                              onPressed: () async {
                                final conn = await MySqlConnection.connect(
                                    ConnectionSettings(
                                        host: controller.hostZain.value,
                                        // port: 80,
                                        user: 'root',
                                        db: 'zain',
                                        password: 'zain',
                                        characterSet: CharacterSet.UTF8));
                                Results getRoomsId1 = await conn.query(
                                    "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Rooms'");

                                List roomsId1 = [];
                                for (var i in getRoomsId1) {
                                  if (i.fields.values.toList()[0] != null) {
                                    roomsId1.add(i.fields.values.toList()[0]);
                                  }
                                }
                                var appDB = await openDatabase(
                                    '${controller.system}.db',
                                    version: 3);
                                // var getRoomsId2 = await appDB.rawQuery('SELECT id FROM rooms');
                                // List roomsId2 = [];
                                // for(var i in getRoomsId2){
                                //   roomsId2.add(i['id']);
                                // }
                                // print(roomsId1);
                                // print(roomsId2);
                                String newRoom;
                                if (roomsId1.isEmpty) {
                                  newRoom = 'room_01';
                                } else {
                                  int n = int.parse(
                                          roomsId1[roomsId1.length - 1]
                                              .split('_')[1]) +
                                      1;

                                  newRoom = n < 10
                                      ? 'room_0' + n.toString()
                                      : 'room_' + n.toString();
                                }
                                await conn.query(
                                    "ALTER TABLE Rooms ADD COLUMN $newRoom VARCHAR(30)");
                                await conn.query(
                                    "insert INTO Rooms(${newRoom}) values(?)", [
                                  name1.text.toString(),
                                ]);
                                await appDB.rawQuery(
                                    'insert into rooms(id,name,image) values(?,?,?)',
                                    [newRoom, name2.text.toString(), image]);
                                conn.close();
                                appDB.close();
                                await 0.35.seconds.delay();

                                // getDevices();
                                Navigator.of(context).pop();

                                print(newRoom);
                              })
                        ],
                      );
                    }),
                  ),
                ]),
          ),
        ),
      ),

      // btnCancelOnPress:
      //     () {},
    ).show();
  }
}
