com.example.zaen.app-lifecycle-viewmodel-2.7.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\21f5521d47a3985f17891551678788af\transformed\lifecycle-viewmodel-2.7.0\res
com.example.zaen.app-lifecycle-livedata-2.7.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\2488d02a42348edc764f80a031727b4e\transformed\lifecycle-livedata-2.7.0\res
com.example.zaen.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\2ef0412457dc84f486400cfaad4953af\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.zaen.app-jetified-profileinstaller-1.3.1-3 C:\Users\<USER>\.gradle\caches\transforms-3\3bb44aec86c50a002486b544b7384d62\transformed\jetified-profileinstaller-1.3.1\res
com.example.zaen.app-lifecycle-livedata-core-2.7.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\3e375bd9b15c7863c0ffd956e3d89824\transformed\lifecycle-livedata-core-2.7.0\res
com.example.zaen.app-jetified-annotation-experimental-1.4.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\46e5d1e806237487d33a7ea5f9ea6e87\transformed\jetified-annotation-experimental-1.4.0\res
com.example.zaen.app-jetified-tracing-1.2.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\497b85a97ee869e66cbf49721ce584f1\transformed\jetified-tracing-1.2.0\res
com.example.zaen.app-lifecycle-runtime-2.7.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\51eac60701b2a6ea45c32cdce7169426\transformed\lifecycle-runtime-2.7.0\res
com.example.zaen.app-jetified-savedstate-1.2.1-8 C:\Users\<USER>\.gradle\caches\transforms-3\5cba2c84661622d304027c2174af8b25\transformed\jetified-savedstate-1.2.1\res
com.example.zaen.app-jetified-startup-runtime-1.1.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\5d61414f224820521e00858832ba1632\transformed\jetified-startup-runtime-1.1.1\res
com.example.zaen.app-jetified-core-1.0.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\6ecefa2f51bd04cfad5a026cff22bfe9\transformed\jetified-core-1.0.0\res
com.example.zaen.app-core-1.13.1-11 C:\Users\<USER>\.gradle\caches\transforms-3\700742d2bb95c414715b88c33fbc9f67\transformed\core-1.13.1\res
com.example.zaen.app-jetified-lifecycle-livedata-core-ktx-2.7.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\72772b42aa2a00700ae9154a92d013b4\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.zaen.app-fragment-1.7.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\774cb9cecc43277739dbfeb362c4b718\transformed\fragment-1.7.1\res
com.example.zaen.app-jetified-lifecycle-process-2.7.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\89f8f2bf3acc3d478784d61204ab295f\transformed\jetified-lifecycle-process-2.7.0\res
com.example.zaen.app-jetified-datastore-1.0.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\a9d7fd95de23e0243db8260125935a3f\transformed\jetified-datastore-1.0.0\res
com.example.zaen.app-core-runtime-2.2.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\b4e9b51431a8dbaf4218f5f1add92c43\transformed\core-runtime-2.2.0\res
com.example.zaen.app-jetified-activity-1.9.2-17 C:\Users\<USER>\.gradle\caches\transforms-3\b588a97f580bfb3e505af5bac86436f3\transformed\jetified-activity-1.9.2\res
com.example.zaen.app-jetified-core-ktx-1.13.1-18 C:\Users\<USER>\.gradle\caches\transforms-3\bde92eedf717fc299f31d6b0932e45af\transformed\jetified-core-ktx-1.13.1\res
com.example.zaen.app-jetified-datastore-preferences-1.0.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\c363a6e185b0bb1ac4e21f23756661da\transformed\jetified-datastore-preferences-1.0.0\res
com.example.zaen.app-jetified-window-1.2.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\c4b2001786faad15787959a904dec94a\transformed\jetified-window-1.2.0\res
com.example.zaen.app-jetified-window-java-1.2.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\ee4c1cfe0cd23a57d7eacd79a18400f0\transformed\jetified-window-java-1.2.0\res
com.example.zaen.app-packaged_res-22 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_keyboard_visibility-6.0.0\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-23 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.23\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-24 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+17\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-25 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\network_info_plus-6.1.1\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-26 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.0.13\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-27 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\rive_common-0.4.12\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-28 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.3.3\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-29 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.0\android\build\intermediates\packaged_res\debug
com.example.zaen.app-packaged_res-30 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\wifi_scan-0.4.1+1\android\build\intermediates\packaged_res\debug
com.example.zaen.app-pngs-31 D:\flutter training\zaen\android\app\build\generated\res\pngs\debug
com.example.zaen.app-resValues-32 D:\flutter training\zaen\android\app\build\generated\res\resValues\debug
com.example.zaen.app-packageDebugResources-33 D:\flutter training\zaen\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.zaen.app-packageDebugResources-34 D:\flutter training\zaen\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.zaen.app-merged_res-35 D:\flutter training\zaen\android\app\build\intermediates\merged_res\debug
com.example.zaen.app-debug-36 D:\flutter training\zaen\android\app\src\debug\res
com.example.zaen.app-main-37 D:\flutter training\zaen\android\app\src\main\res
