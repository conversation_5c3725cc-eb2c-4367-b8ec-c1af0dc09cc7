# نظام الثيمات والألوان الجديد

## نظرة عامة

تم إنشاء نظام جديد لإدارة الألوان والثيمات في التطبيق يدعم الوضع الداكن والفاتح مع إمكانية التبديل بينهما وحفظ الإعدادات.

## الملفات الرئيسية

### 1. `app_colors.dart`
يحتوي على تعريف جميع الألوان للوضع الداكن والفاتح:
- `AppColors.light`: ألوان الوضع الفاتح
- `AppColors.dark`: ألوان الوضع الداكن
- `AppColors.current`: الألوان الحالية حسب الوضع المختار

### 2. `theme_manager.dart`
يحتوي على تعريف الثيمات الكاملة:
- `ThemeManager.lightTheme`: الثيم الفاتح
- `ThemeManager.darkTheme`: الثيم الداكن

### 3. `settings.dart`
كونترولر لإدارة إعدادات التطبيق:
- `SettingsController`: يدير حالة الوضع الداكن/الفاتح
- `toggleTheme()`: تبديل الوضع
- `setDarkMode(bool)`: تعيين وضع محدد

### 4. `color_migration.dart`
ملف مساعد للانتقال من النظام القديم:
- يحتوي على متغيرات بنفس أسماء الألوان القديمة
- يسهل عملية الانتقال دون كسر الكود الموجود

### 5. `theme_settings.dart`
واجهة المستخدم لإعدادات الثيم:
- صفحة لاختيار الوضع الداكن أو الفاتح
- حفظ الإعدادات تلقائياً

## كيفية الاستخدام

### 1. الحصول على الألوان
```dart
// الطريقة الجديدة
Color primaryColor = AppColors.primaryColor;
Color backgroundColor = AppColors.backgroundColor;
Color textColor = AppColors.textColor;

// أو باستخدام الطريقة القديمة (للتوافق)
Color switchColor = colorSW;
Color bgColor = colorBG;
```

### 2. تبديل الثيم
```dart
final settingsController = Get.find<SettingsController>();
await settingsController.toggleTheme();
```

### 3. تعيين وضع محدد
```dart
// تفعيل الوضع الداكن
await settingsController.setDarkMode(true);

// تفعيل الوضع الفاتح
await settingsController.setDarkMode(false);
```

### 4. الاستماع لتغييرات الثيم
```dart
Obx(() => Container(
  color: AppColors.backgroundColor,
  child: Text(
    'النص',
    style: TextStyle(color: AppColors.textColor),
  ),
));
```

## الألوان المتاحة

### الألوان الأساسية
- `primary`: اللون الأساسي (أخضر)
- `secondary`: اللون الثانوي (أزرق)
- `background`: خلفية التطبيق
- `surface`: سطح الكروت والعناصر
- `error`: لون الخطأ (أحمر)
- `success`: لون النجاح (أخضر)
- `warning`: لون التحذير (أصفر)
- `info`: لون المعلومات (أزرق)

### ألوان النصوص
- `textColor`: لون النص الأساسي
- `secondaryTextColor`: لون النص الثانوي
- `onPrimary`: نص على اللون الأساسي
- `onSurface`: نص على السطح

## التهيئة

### في main.dart
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة كونترولر الإعدادات
  Get.put(SettingsController());
  
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      theme: ThemeManager.lightTheme,
      darkTheme: ThemeManager.darkTheme,
      themeMode: ThemeMode.system,
      home: const HomePage(),
    );
  }
}
```

## المميزات

1. **دعم الوضع الداكن والفاتح**: تبديل سهل بين الوضعين
2. **حفظ الإعدادات**: يتم حفظ اختيار المستخدم تلقائياً
3. **تحديث فوري**: تغيير الثيم فوراً دون إعادة تشغيل
4. **توافق مع النظام القديم**: لا يكسر الكود الموجود
5. **سهولة الصيانة**: نظام منظم وقابل للتوسع
6. **أداء محسن**: استخدام GetX للإدارة الفعالة للحالة

## الانتقال من النظام القديم

تم الحفاظ على جميع أسماء الألوان القديمة في `color_migration.dart`:
- `colorSW` → `AppColors.primaryColor`
- `colorBG` → `AppColors.backgroundColor`
- `colorBT` → `AppColors.textColor`
- وهكذا...

هذا يضمن عدم كسر أي كود موجود أثناء الانتقال التدريجي للنظام الجديد.
