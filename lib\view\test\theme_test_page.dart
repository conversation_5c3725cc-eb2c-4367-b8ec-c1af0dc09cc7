import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/view/home/<USER>/theme_settings.dart';

class ThemeTestPage extends StatelessWidget {
  const ThemeTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsController = Get.find<SettingsController>();
    
    return Scaffold(
      appBar: AppBar(
        title: txtStyle(
          txt: 'اختبار نظام الثيمات',
          color: AppColors.textColor,
        ),
        backgroundColor: AppColors.surfaceColor,
        actions: [
          IconButton(
            icon: Icon(
              Icons.settings,
              color: AppColors.textColor,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ThemeSettingsPage(),
                ),
              );
            },
          ),
        ],
      ),
      backgroundColor: AppColors.backgroundColor,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الثيم الحالي
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surfaceColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.current.shadow,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppColors.current.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      txtStyle(
                        txt: 'معلومات الثيم الحالي',
                        color: AppColors.textColor,
                        size: 18,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Obx(() => txtStyle(
                    txt: 'الوضع الحالي: ${settingsController.currentThemeName}',
                    color: AppColors.secondaryTextColor,
                    size: 14,
                  )),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // زر التبديل السريع
            Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  await settingsController.toggleTheme();
                },
                icon: Obx(() => Icon(
                  settingsController.isDarkMode.value 
                    ? Icons.light_mode 
                    : Icons.dark_mode,
                )),
                label: Obx(() => txtStyle(
                  txt: settingsController.isDarkMode.value 
                    ? 'تبديل للوضع الفاتح' 
                    : 'تبديل للوضع الداكن',
                  color: AppColors.current.onPrimary,
                )),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.current.primary,
                  foregroundColor: AppColors.current.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // عرض الألوان
            txtStyle(
              txt: 'عرض الألوان',
              color: AppColors.textColor,
              size: 18,
            ),
            const SizedBox(height: 16),
            
            // شبكة الألوان
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildColorCard('الأساسي', AppColors.current.primary),
                _buildColorCard('الثانوي', AppColors.current.secondary),
                _buildColorCard('الخلفية', AppColors.current.background),
                _buildColorCard('السطح', AppColors.current.surface),
                _buildColorCard('الخطأ', AppColors.current.error),
                _buildColorCard('النجاح', AppColors.current.success),
                _buildColorCard('التحذير', AppColors.current.warning),
                _buildColorCard('المعلومات', AppColors.current.info),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // اختبار المكونات
            txtStyle(
              txt: 'اختبار المكونات',
              color: AppColors.textColor,
              size: 18,
            ),
            const SizedBox(height: 16),
            
            // أزرار مختلفة
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    child: txtStyle(txt: 'زر أساسي', color: AppColors.current.onPrimary),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.current.primary,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: txtStyle(txt: 'زر ثانوي', color: AppColors.current.primary),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppColors.current.primary),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // بطاقة تجريبية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surfaceColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.current.outline,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  txtStyle(
                    txt: 'بطاقة تجريبية',
                    color: AppColors.textColor,
                    size: 16,
                  ),
                  const SizedBox(height: 8),
                  txtStyle(
                    txt: 'هذه بطاقة تجريبية لاختبار الألوان والتصميم في الوضع الحالي',
                    color: AppColors.secondaryTextColor,
                    size: 14,
                    maxLines: 2,
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Icon(
                        Icons.favorite,
                        color: AppColors.current.error,
                        size: 20,
                      ),
                      Icon(
                        Icons.share,
                        color: AppColors.current.secondary,
                        size: 20,
                      ),
                      Icon(
                        Icons.bookmark,
                        color: AppColors.current.warning,
                        size: 20,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // رسالة تأكيد
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.current.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.current.success.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    color: AppColors.current.success,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: txtStyle(
                      txt: 'تم تطبيق نظام الثيمات الجديد بنجاح! يمكنك الآن التبديل بين الوضع الداكن والفاتح.',
                      color: AppColors.current.success,
                      size: 14,
                      maxLines: 3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildColorCard(String name, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: txtStyle(
          txt: name,
          color: color.computeLuminance() > 0.5 ? Colors.black : Colors.white,
          size: 12,
        ),
      ),
    );
  }
}
